#!/usr/bin/env node

/**
 * Workflow Validation Script
 * 
 * Validates all workflow JSON files for syntax, structure, and completeness
 * before deployment to ensure ecosystem integrity.
 */

const fs = require('fs').promises;
const path = require('path');

// Validation Configuration
const VALIDATION_CONFIG = {
  productFlowsPath: path.join(__dirname, '../product_flows'),
  infrastructureFlowsPath: path.join(__dirname, '../infrastructure_flows'),
  automationFlowsPath: path.join(__dirname, '../automation'),
  requiredFields: ['name', 'nodes', 'connections'],
  requiredNodeFields: ['id', 'name', 'type', 'position'],
  expectedWorkflows: [
    'WhatsApp_Message_Router_v1.json',
    'SuperUser_Authentication_v1.json',
    'Session_Manager_v1.json',
    'Main_Orchestrator_Agent_v1.json',
    'Message_Processor_v1.json'
  ]
};

class WorkflowValidator {
  constructor() {
    this.validationResults = [];
    this.errors = [];
    this.warnings = [];
  }

  // Validate JSON syntax
  validateJsonSyntax(filePath, content) {
    try {
      const parsed = JSON.parse(content);
      return { valid: true, data: parsed };
    } catch (error) {
      return { 
        valid: false, 
        error: `JSON syntax error: ${error.message}`,
        data: null 
      };
    }
  }

  // Validate workflow structure
  validateWorkflowStructure(workflow, filename) {
    const issues = [];
    
    // Check required top-level fields
    for (const field of VALIDATION_CONFIG.requiredFields) {
      if (!workflow[field]) {
        issues.push(`Missing required field: ${field}`);
      }
    }
    
    // Validate workflow name
    if (workflow.name && !workflow.name.includes('v1')) {
      issues.push('Workflow name should include version (v1)');
    }
    
    // Validate nodes array
    if (workflow.nodes && Array.isArray(workflow.nodes)) {
      workflow.nodes.forEach((node, index) => {
        // Check required node fields
        for (const field of VALIDATION_CONFIG.requiredNodeFields) {
          if (!node[field]) {
            issues.push(`Node ${index}: Missing required field: ${field}`);
          }
        }
        
        // Validate node ID uniqueness
        const duplicateIds = workflow.nodes.filter(n => n.id === node.id);
        if (duplicateIds.length > 1) {
          issues.push(`Node ${index}: Duplicate node ID: ${node.id}`);
        }
        
        // Validate position format
        if (node.position && (!Array.isArray(node.position) || node.position.length !== 2)) {
          issues.push(`Node ${index}: Invalid position format (should be [x, y])`);
        }
      });
    } else {
      issues.push('Nodes field should be an array');
    }
    
    // Validate connections
    if (workflow.connections && typeof workflow.connections === 'object') {
      Object.keys(workflow.connections).forEach(nodeKey => {
        const nodeExists = workflow.nodes?.some(node => node.name === nodeKey);
        if (!nodeExists) {
          issues.push(`Connection references non-existent node: ${nodeKey}`);
        }
      });
    }
    
    return issues;
  }

  // Validate workflow dependencies
  validateWorkflowDependencies(workflow, filename) {
    const issues = [];
    
    // Check for executeWorkflow nodes and validate references
    if (workflow.nodes) {
      workflow.nodes.forEach((node, index) => {
        if (node.type === 'n8n-nodes-base.executeWorkflow') {
          const workflowId = node.parameters?.workflowId;
          if (workflowId) {
            // Check if referenced workflow exists in expected workflows
            const referencedWorkflow = workflowId.replace(/ /g, '_') + '.json';
            if (!VALIDATION_CONFIG.expectedWorkflows.includes(referencedWorkflow)) {
              issues.push(`Node ${index}: References unknown workflow: ${workflowId}`);
            }
          }
        }
      });
    }
    
    return issues;
  }

  // Validate environment variable usage
  validateEnvironmentVariables(workflow, filename) {
    const issues = [];
    const envVarsUsed = [];
    
    // Extract environment variables from workflow JSON string
    const workflowString = JSON.stringify(workflow);
    const envVarMatches = workflowString.match(/\$env\.[A-Z_]+/g);
    
    if (envVarMatches) {
      envVarsUsed.push(...envVarMatches.map(match => match.replace('$env.', '')));
    }
    
    // Check for required environment variables
    const requiredEnvVars = [
      'N8N_API_URL',
      'N8N_API_KEY',
      'EVOLUTION_API_URL',
      'EVOLUTION_API_KEY',
      'EVOLUTION_INSTANCE'
    ];
    
    const missingEnvVars = requiredEnvVars.filter(envVar => 
      envVarsUsed.includes(envVar) && !process.env[envVar]
    );
    
    if (missingEnvVars.length > 0) {
      issues.push(`Missing environment variables: ${missingEnvVars.join(', ')}`);
    }
    
    return { issues, envVarsUsed: [...new Set(envVarsUsed)] };
  }

  // Validate single workflow file
  async validateWorkflowFile(filePath) {
    const filename = path.basename(filePath);
    const result = {
      filename,
      path: filePath,
      valid: true,
      issues: [],
      warnings: [],
      metadata: {}
    };
    
    try {
      // Read file content
      const content = await fs.readFile(filePath, 'utf8');
      
      // Validate JSON syntax
      const jsonValidation = this.validateJsonSyntax(filePath, content);
      if (!jsonValidation.valid) {
        result.valid = false;
        result.issues.push(jsonValidation.error);
        return result;
      }
      
      const workflow = jsonValidation.data;
      
      // Validate workflow structure
      const structureIssues = this.validateWorkflowStructure(workflow, filename);
      result.issues.push(...structureIssues);
      
      // Validate dependencies
      const dependencyIssues = this.validateWorkflowDependencies(workflow, filename);
      result.issues.push(...dependencyIssues);
      
      // Validate environment variables
      const envValidation = this.validateEnvironmentVariables(workflow, filename);
      result.issues.push(...envValidation.issues);
      result.metadata.envVarsUsed = envValidation.envVarsUsed;
      
      // Set overall validity
      result.valid = result.issues.length === 0;
      
      // Add metadata
      result.metadata.nodeCount = workflow.nodes?.length || 0;
      result.metadata.workflowName = workflow.name;
      result.metadata.hasWebhook = workflow.nodes?.some(node => 
        node.type === 'n8n-nodes-base.webhook'
      ) || false;
      
    } catch (error) {
      result.valid = false;
      result.issues.push(`File read error: ${error.message}`);
    }
    
    return result;
  }

  // Validate all workflows in a directory
  async validateDirectory(directoryPath, directoryName) {
    const results = [];
    
    try {
      const files = await fs.readdir(directoryPath);
      const jsonFiles = files.filter(file => file.endsWith('.json'));
      
      console.log(`📁 Validating ${jsonFiles.length} workflows in ${directoryName}...`);
      
      for (const file of jsonFiles) {
        const filePath = path.join(directoryPath, file);
        const result = await this.validateWorkflowFile(filePath);
        results.push(result);
        
        // Log progress
        const status = result.valid ? '✅' : '❌';
        console.log(`   ${status} ${file}`);
        
        if (!result.valid) {
          result.issues.forEach(issue => console.log(`      - ${issue}`));
        }
      }
      
    } catch (error) {
      console.error(`❌ Error reading directory ${directoryPath}:`, error.message);
    }
    
    return results;
  }

  // Run complete validation
  async runValidation() {
    console.log('🔍 Workflow Validation Suite\n');
    
    const allResults = [];
    
    // Validate product flows
    console.log('📋 Validating Product Flows...');
    const productResults = await this.validateDirectory(
      VALIDATION_CONFIG.productFlowsPath, 
      'product_flows'
    );
    allResults.push(...productResults);
    
    // Validate infrastructure flows (if exists)
    console.log('\n🏗️ Validating Infrastructure Flows...');
    try {
      await fs.access(VALIDATION_CONFIG.infrastructureFlowsPath);
      const infraResults = await this.validateDirectory(
        VALIDATION_CONFIG.infrastructureFlowsPath, 
        'infrastructure_flows'
      );
      allResults.push(...infraResults);
    } catch (error) {
      console.log('   ⚠️ Infrastructure flows directory not found (optional)');
    }
    
    // Validate automation flows
    console.log('\n🤖 Validating Automation Flows...');
    const automationResults = await this.validateDirectory(
      VALIDATION_CONFIG.automationFlowsPath, 
      'automation'
    );
    allResults.push(...automationResults);
    
    // Generate summary
    const summary = this.generateValidationSummary(allResults);
    
    // Save validation report
    await this.saveValidationReport(allResults, summary);
    
    // Display results
    this.displayValidationResults(summary);
    
    return { results: allResults, summary };
  }

  // Generate validation summary
  generateValidationSummary(results) {
    const total = results.length;
    const valid = results.filter(r => r.valid).length;
    const invalid = results.filter(r => !r.valid).length;
    const totalIssues = results.reduce((sum, r) => sum + r.issues.length, 0);
    
    return {
      total_workflows: total,
      valid_workflows: valid,
      invalid_workflows: invalid,
      total_issues: totalIssues,
      success_rate: total > 0 ? ((valid / total) * 100).toFixed(1) + '%' : '0%',
      validation_timestamp: new Date().toISOString()
    };
  }

  // Save validation report
  async saveValidationReport(results, summary) {
    const reportPath = path.join(__dirname, `validation_report_${Date.now()}.json`);
    
    const report = {
      validation_info: {
        timestamp: new Date().toISOString(),
        validator_version: '1.0.0',
        config: VALIDATION_CONFIG
      },
      summary: summary,
      detailed_results: results
    };
    
    try {
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n📄 Validation report saved: ${reportPath}`);
    } catch (error) {
      console.warn('⚠️ Failed to save validation report:', error.message);
    }
  }

  // Display validation results
  displayValidationResults(summary) {
    console.log('\n📊 Validation Summary');
    console.log('=' .repeat(50));
    console.log(`📋 Total Workflows: ${summary.total_workflows}`);
    console.log(`✅ Valid: ${summary.valid_workflows}`);
    console.log(`❌ Invalid: ${summary.invalid_workflows}`);
    console.log(`🐛 Total Issues: ${summary.total_issues}`);
    console.log(`📈 Success Rate: ${summary.success_rate}`);
    console.log('=' .repeat(50));
    
    if (summary.invalid_workflows > 0) {
      console.log('\n⚠️ Some workflows have validation issues. Please review and fix before deployment.');
      process.exit(1);
    } else {
      console.log('\n🎉 All workflows passed validation! Ready for deployment.');
      process.exit(0);
    }
  }
}

// CLI execution
if (require.main === module) {
  const validator = new WorkflowValidator();
  validator.runValidation();
}

module.exports = WorkflowValidator;
