#!/usr/bin/env node

/**
 * Workflow Activation Script
 * 
 * This script activates all the core workflows that should be running.
 * It identifies inactive workflows and activates them in the correct order.
 * 
 * Usage: node automation/activate_workflows.js
 */

require('dotenv').config();
const N8nApiClient = require('./n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL,
  n8nApiKey: process.env.N8N_API_KEY
};

// Workflows that should be active (in activation order)
const CORE_WORKFLOWS = [
  'SuperUser Authentication v1',
  'Session Manager v1', 
  'Main Orchestrator Agent v2',
  'Message Processor v1',
  'WhatsApp Message Router v1' // Router last as it triggers the chain
];

class WorkflowActivator {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.activationResults = [];
  }

  // Main activation process
  async activateAllWorkflows() {
    console.log('🚀 Starting Workflow Activation Process\n');
    console.log('🎯 Target n8n Instance:', CONFIG.n8nApiUrl);
    console.log('📋 Core workflows to activate:', CORE_WORKFLOWS.length);
    console.log('');

    try {
      // Step 1: Get current workflow status
      const workflows = await this.client.getWorkflows();
      console.log(`📊 Found ${workflows.data?.length || 0} total workflows\n`);

      // Step 2: Identify workflows to activate
      const workflowsToActivate = [];
      const alreadyActive = [];
      const notFound = [];

      for (const workflowName of CORE_WORKFLOWS) {
        const workflow = workflows.data?.find(w => w.name === workflowName);
        
        if (!workflow) {
          notFound.push(workflowName);
          console.log(`❌ Not found: ${workflowName}`);
        } else if (workflow.active) {
          alreadyActive.push(workflowName);
          console.log(`✅ Already active: ${workflowName}`);
        } else {
          workflowsToActivate.push({
            name: workflowName,
            id: workflow.id
          });
          console.log(`⏳ Will activate: ${workflowName} (ID: ${workflow.id})`);
        }
      }

      console.log('');

      // Step 3: Activate workflows
      if (workflowsToActivate.length > 0) {
        console.log(`🔄 Activating ${workflowsToActivate.length} workflows...\n`);
        
        for (let i = 0; i < workflowsToActivate.length; i++) {
          const workflow = workflowsToActivate[i];
          await this.activateWorkflow(workflow, i + 1, workflowsToActivate.length);
          
          // Add delay between activations
          if (i < workflowsToActivate.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      } else {
        console.log('✅ All core workflows are already active!\n');
      }

      // Step 4: Generate report
      await this.generateActivationReport({
        total: CORE_WORKFLOWS.length,
        alreadyActive: alreadyActive.length,
        activated: workflowsToActivate.length,
        notFound: notFound.length,
        alreadyActiveList: alreadyActive,
        activatedList: workflowsToActivate.map(w => w.name),
        notFoundList: notFound
      });

      const success = notFound.length === 0;
      console.log(success ? '🎉 Workflow activation completed successfully!' : '⚠️  Workflow activation completed with issues');
      return success;

    } catch (error) {
      console.error('❌ Workflow activation failed:', error.message);
      return false;
    }
  }

  // Activate individual workflow
  async activateWorkflow(workflow, current, total) {
    try {
      console.log(`[${current}/${total}] Activating: ${workflow.name}...`);
      
      const result = await this.client.activateWorkflow(workflow.id);
      
      this.activationResults.push({
        name: workflow.name,
        id: workflow.id,
        status: 'SUCCESS',
        result: result
      });
      
      console.log(`  ✅ Successfully activated: ${workflow.name}`);
      
    } catch (error) {
      console.error(`  ❌ Failed to activate ${workflow.name}:`, error.message);
      
      this.activationResults.push({
        name: workflow.name,
        id: workflow.id,
        status: 'ERROR',
        error: error.message
      });
    }
  }

  // Generate activation report
  async generateActivationReport(summary) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = `workflow_activation_report_${timestamp}.json`;

    const report = {
      timestamp: new Date().toISOString(),
      n8nInstance: CONFIG.n8nApiUrl,
      summary: summary,
      activationResults: this.activationResults,
      recommendations: this.generateRecommendations(summary)
    };

    const fs = require('fs').promises;
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log('\n📊 ACTIVATION REPORT');
    console.log('=' .repeat(50));
    console.log(`📄 Report saved to: ${reportPath}`);
    console.log(`📋 Total workflows: ${summary.total}`);
    console.log(`✅ Already active: ${summary.alreadyActive}`);
    console.log(`🔄 Newly activated: ${summary.activated}`);
    console.log(`❌ Not found: ${summary.notFound}`);
    
    if (summary.notFoundList.length > 0) {
      console.log('\n❌ Missing workflows:');
      summary.notFoundList.forEach(name => console.log(`  - ${name}`));
    }

    if (report.recommendations.length > 0) {
      console.log('\n📋 RECOMMENDATIONS:');
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }

  // Generate recommendations based on results
  generateRecommendations(summary) {
    const recommendations = [];

    if (summary.notFound > 0) {
      recommendations.push('Deploy missing workflows using the deployment script');
    }

    if (summary.activated > 0) {
      recommendations.push('Test the activated workflows to ensure they work correctly');
    }

    if (this.activationResults.some(r => r.status === 'ERROR')) {
      recommendations.push('Review activation errors and fix workflow configuration issues');
    }

    if (summary.alreadyActive === summary.total && summary.notFound === 0) {
      recommendations.push('All workflows are active - test the complete message processing pipeline');
    }

    return recommendations;
  }

  // Check workflow status
  async checkWorkflowStatus() {
    console.log('🔍 Checking current workflow status...\n');

    try {
      const workflows = await this.client.getWorkflows();
      const coreWorkflowStatus = [];

      for (const workflowName of CORE_WORKFLOWS) {
        const workflow = workflows.data?.find(w => w.name === workflowName);
        
        if (workflow) {
          coreWorkflowStatus.push({
            name: workflowName,
            id: workflow.id,
            active: workflow.active,
            status: workflow.active ? '✅ Active' : '⏸️  Inactive'
          });
        } else {
          coreWorkflowStatus.push({
            name: workflowName,
            id: null,
            active: false,
            status: '❌ Not Found'
          });
        }
      }

      console.log('📋 Core Workflow Status:');
      console.log('-' .repeat(60));
      coreWorkflowStatus.forEach(workflow => {
        console.log(`${workflow.status.padEnd(12)} ${workflow.name}`);
      });
      console.log('');

      return coreWorkflowStatus;

    } catch (error) {
      console.error('❌ Failed to check workflow status:', error.message);
      return [];
    }
  }
}

// Main execution
async function main() {
  if (!CONFIG.n8nApiKey) {
    console.error('❌ N8N_API_KEY environment variable is required');
    process.exit(1);
  }

  const activator = new WorkflowActivator();
  
  // Check current status first
  await activator.checkWorkflowStatus();
  
  // Activate workflows
  const success = await activator.activateAllWorkflows();
  
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = WorkflowActivator;
