{"validation_info": {"timestamp": "2025-08-26T21:03:51.934Z", "validator_version": "1.0.0", "config": {"productFlowsPath": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows", "infrastructureFlowsPath": "C:\\Users\\<USER>\\repos\\AI Comtxae\\infrastructure_flows", "automationFlowsPath": "C:\\Users\\<USER>\\repos\\AI Comtxae\\automation", "requiredFields": ["name", "nodes", "connections"], "requiredNodeFields": ["id", "name", "type", "position"], "expectedWorkflows": ["WhatsApp_Message_Router_v1.json", "SuperUser_Authentication_v1.json", "Session_Manager_v1.json", "Main_Orchestrator_Agent_v1.json", "Message_Processor_v1.json"]}}, "summary": {"total_workflows": 12, "valid_workflows": 5, "invalid_workflows": 7, "total_issues": 9, "success_rate": "41.7%", "validation_timestamp": "2025-08-26T21:03:51.933Z"}, "detailed_results": [{"filename": "Agentes SuperUsuario.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows\\Agentes SuperUsuario.json", "valid": false, "issues": ["Missing required field: name"], "warnings": [], "metadata": {"envVarsUsed": [], "nodeCount": 9, "hasWebhook": false}}, {"filename": "Main_Orchestrator_Agent_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows\\Main_Orchestrator_Agent_v1.json", "valid": true, "issues": [], "warnings": [], "metadata": {"envVarsUsed": [], "nodeCount": 11, "workflowName": "Main Orchestrator Agent v1", "hasWebhook": false}}, {"filename": "Message_Processor_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows\\Message_Processor_v1.json", "valid": false, "issues": ["Missing environment variables: EVOLUTION_API_URL, EVOLUTION_API_KEY, EVOLUTION_INSTANCE"], "warnings": [], "metadata": {"envVarsUsed": ["EVOLUTION_API_URL", "EVOLUTION_INSTANCE", "EVOLUTION_API_KEY"], "nodeCount": 10, "workflowName": "Message Processor v1", "hasWebhook": false}}, {"filename": "Roteamento Webhook EVO.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows\\Roteamento Webhook EVO.json", "valid": false, "issues": ["Workflow name should include version (v1)", "File read error: workflowId.replace is not a function"], "warnings": [], "metadata": {}}, {"filename": "Session_Manager_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows\\Session_Manager_v1.json", "valid": true, "issues": [], "warnings": [], "metadata": {"envVarsUsed": [], "nodeCount": 10, "workflowName": "Session Manager v1", "hasWebhook": false}}, {"filename": "SuperUser_Authentication_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows\\SuperUser_Authentication_v1.json", "valid": true, "issues": [], "warnings": [], "metadata": {"envVarsUsed": [], "nodeCount": 10, "workflowName": "SuperUser Authentication v1", "hasWebhook": false}}, {"filename": "Tratamento MSG SuperUsuario.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows\\Tratamento MSG SuperUsuario.json", "valid": false, "issues": ["Workflow name should include version (v1)", "File read error: workflowId.replace is not a function"], "warnings": [], "metadata": {}}, {"filename": "WhatsApp_Message_Router_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\product_flows\\WhatsApp_Message_Router_v1.json", "valid": false, "issues": ["Missing environment variables: EVOLUTION_API_URL, EVOLUTION_API_KEY, EVOLUTION_INSTANCE"], "warnings": [], "metadata": {"envVarsUsed": ["EVOLUTION_API_URL", "EVOLUTION_INSTANCE", "EVOLUTION_API_KEY"], "nodeCount": 11, "workflowName": "WhatsApp Message Router v1", "hasWebhook": true}}, {"filename": "Database_Schema_Generator_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\infrastructure_flows\\Database_Schema_Generator_v1.json", "valid": true, "issues": [], "warnings": [], "metadata": {"envVarsUsed": [], "nodeCount": 10, "workflowName": "Database_Schema_Generator_v1", "hasWebhook": false}}, {"filename": "Bulk_Deployment_Orchestrator_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\automation\\Bulk_Deployment_Orchestrator_v1.json", "valid": false, "issues": ["Missing required field: connections"], "warnings": [], "metadata": {"envVarsUsed": ["N"], "nodeCount": 6, "workflowName": "Bulk Deployment Orchestrator v1", "hasWebhook": false}}, {"filename": "Git_Workflow_Sync_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\automation\\Git_Workflow_Sync_v1.json", "valid": true, "issues": [], "warnings": [], "metadata": {"envVarsUsed": ["GITHUB_TOKEN"], "nodeCount": 5, "workflowName": "Git Workflow Sync v1", "hasWebhook": false}}, {"filename": "Workflow_Generator_Master_v1.json", "path": "C:\\Users\\<USER>\\repos\\AI Comtxae\\automation\\Workflow_Generator_Master_v1.json", "valid": false, "issues": ["Missing required field: connections"], "warnings": [], "metadata": {"envVarsUsed": ["N"], "nodeCount": 4, "workflowName": "Workflow Generator Master v1", "hasWebhook": false}}]}