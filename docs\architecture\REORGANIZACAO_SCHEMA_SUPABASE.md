# 🗄️ REORGANIZAÇÃO DO SCHEMA SUPABASE - 16 TABELAS

**Data**: 18/09/2025  
**Objetivo**: Unificar e organizar as 16 tabelas existentes no Supabase  
**Estratégia**: Resolver conexões legado, integrar tabelas desconectadas, conectar isoladas  

---

## 📊 ANÁLISE ATUAL DAS 16 TABELAS

### **GRUPO 1: SISTEMA PRINCIPAL (9 tabelas) - ✅ CONECTADAS**
1. **`super_users`** - Tabela legado (centro do sistema atual)
2. **`chat_sessions`** - Sessões de chat (FK → super_users)
3. **`chat_messages`** - Mensagens (FK → chat_sessions, super_users)
4. **`chat_memory_history`** - Histórico de memória (FK → chat_sessions, super_users)
5. **`user_contact_methods`** - Métodos de contato (FK → super_users)
6. **`user_qa_context`** - Contexto Q&A (FK → super_users)
7. **`authentication_logs`** - Logs de auth (FK → super_users)
8. **`appointments`** - Agendamentos (FK → super_users)
9. **`documents`** - Documentos (FK → super_users)

### **GRUPO 2: SISTEMA COMUNITÁRIO (3 tabelas) - ⚠️ DESCONECTADAS**
10. **`community_leaders`** - Líderes comunitários (isolada)
11. **`community_needs_assessments`** - Avaliações (FK → community_leaders)
12. **`community_needs_items`** - Itens de necessidades (FK → community_needs_assessments)

### **GRUPO 3: SISTEMA NOVO (4 tabelas) - ❌ ISOLADAS**
13. **`users`** - Nova tabela principal (CPF como PK)
14. **`user_context`** - Contexto do usuário (isolada)
15. **`user_development_matrix`** - Matriz de desenvolvimento (isolada)
16. **`user_roles`** - Roles do usuário (isolada)

---

## 🎯 PROBLEMAS IDENTIFICADOS

### **1. DUPLICAÇÃO DE SISTEMAS**
- **`super_users`** (legado) vs **`users`** (novo)
- Dois sistemas de identificação: `id` vs `cpf`
- Dados fragmentados entre sistemas

### **2. TABELAS DESCONECTADAS**
- Sistema comunitário (`community_*`) não conectado ao principal
- Líderes comunitários não são usuários do sistema
- Perda de integração e funcionalidades

### **3. TABELAS ISOLADAS**
- `user_context`, `user_development_matrix`, `user_roles` não conectadas
- Funcionalidades implementadas mas não utilizáveis
- Desperdício de desenvolvimento

### **4. INCONSISTÊNCIAS DE TIPOS**
- `user_id` como `bigint` vs `character varying`
- Relacionamentos quebrados por tipos incompatíveis
- Queries complexas desnecessárias

---

## 🔧 PLANO DE REORGANIZAÇÃO

### **FASE 1: UNIFICAÇÃO DO SISTEMA DE USUÁRIOS**

#### **1.1 Migrar `super_users` → `users`**
```sql
-- Migração de dados
INSERT INTO users (cpf, phone, name, email, access_level, active, last_access, metadata, created_at, updated_at)
SELECT 
  COALESCE(canonical_user_key, LPAD(id::text, 11, '0')) as cpf, -- Gerar CPF se não existir
  phone,
  name,
  email,
  access_level,
  active,
  last_access,
  metadata,
  created_at,
  updated_at
FROM super_users;
```

#### **1.2 Atualizar FKs para usar CPF**
- Alterar todas as tabelas que referenciam `super_users.id`
- Usar `users.cpf` como nova referência
- Manter compatibilidade durante transição

### **FASE 2: CONECTAR SISTEMA COMUNITÁRIO**

#### **2.1 Integrar `community_leaders` → `users`**
```sql
-- Adicionar líderes como usuários
INSERT INTO users (cpf, phone, nome, access_level, metadata, created_at, updated_at)
SELECT 
  LPAD(EXTRACT(EPOCH FROM created_at)::bigint::text, 11, '0') as cpf, -- CPF temporário
  phone,
  nome as name,
  7 as access_level, -- Nível máximo para líderes
  jsonb_build_object(
    'tipo_lideranca', tipo_lideranca,
    'comunidade', comunidade,
    'localizacao', localizacao,
    'cargo', cargo,
    'migrated_from', 'community_leaders'
  ) as metadata,
  created_at,
  updated_at
FROM community_leaders;
```

#### **2.2 Conectar avaliações e necessidades**
- Conectar `community_needs_assessments` via CPF do líder
- Manter funcionalidade específica comunitária
- Integrar com sistema principal de usuários

### **FASE 3: CONECTAR TABELAS ISOLADAS**

#### **3.1 Conectar `user_context`**
```sql
-- Alterar FK para usar CPF
ALTER TABLE user_context 
ADD COLUMN user_cpf character varying REFERENCES users(cpf);

-- Migrar dados existentes (se houver)
UPDATE user_context 
SET user_cpf = (SELECT cpf FROM users WHERE id::text = user_context.user_id LIMIT 1);

-- Remover coluna antiga
ALTER TABLE user_context DROP COLUMN user_id;
ALTER TABLE user_context RENAME COLUMN user_cpf TO user_cpf;
```

#### **3.2 Conectar `user_development_matrix`**
```sql
-- Mesmo processo para matriz de desenvolvimento
ALTER TABLE user_development_matrix 
ADD COLUMN user_cpf character varying REFERENCES users(cpf);
-- ... migração similar
```

#### **3.3 Conectar `user_roles`**
```sql
-- Mesmo processo para roles
ALTER TABLE user_roles 
ADD COLUMN user_cpf character varying REFERENCES users(cpf);
-- ... migração similar
```

---

## 🏗️ NOVA ARQUITETURA UNIFICADA

### **TABELA CENTRAL: `users`**
```sql
-- Tabela principal unificada
users (
  cpf VARCHAR(11) PRIMARY KEY,  -- Identificador único
  id BIGINT UNIQUE,             -- Compatibilidade legado
  phone VARCHAR NOT NULL,       -- Múltiplos phones permitidos
  name VARCHAR NOT NULL,
  email VARCHAR,
  access_level INTEGER (1-7),   -- Sistema gamificado
  geolocation JSONB,
  onboarding_progress JSONB,    -- Progressão gamificada
  active BOOLEAN DEFAULT true,
  last_access TIMESTAMP,
  metadata JSONB,               -- Dados flexíveis
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### **RELACIONAMENTOS UNIFICADOS**
```
users (CPF) ←─┐
              ├─ chat_sessions
              ├─ chat_messages  
              ├─ chat_memory_history
              ├─ user_contact_methods
              ├─ user_qa_context
              ├─ authentication_logs
              ├─ appointments
              ├─ documents
              ├─ user_context
              ├─ user_development_matrix
              ├─ user_roles
              └─ community_needs_assessments
```

---

## 🔄 SCRIPT DE MIGRAÇÃO COMPLETO

### **Passo 1: Backup**
```sql
-- Criar backup de todas as tabelas
CREATE TABLE super_users_backup AS SELECT * FROM super_users;
CREATE TABLE community_leaders_backup AS SELECT * FROM community_leaders;
-- ... outros backups
```

### **Passo 2: Migração de Dados**
```sql
-- 1. Migrar super_users → users
-- 2. Migrar community_leaders → users  
-- 3. Atualizar todas as FKs
-- 4. Conectar tabelas isoladas
-- 5. Validar integridade
```

### **Passo 3: Limpeza**
```sql
-- Remover tabelas legado após validação
-- DROP TABLE super_users;
-- DROP TABLE community_leaders; (manter como view)
```

---

## 📋 ATUALIZAÇÃO DOS PROMPTS DOS AGENTES

### **Context 7 MCP**
- Library ID: `/supabase/supabase` para operações Supabase
- Incluir schema unificado nas consultas
- Validar tipos de dados e relacionamentos

### **Prompts Atualizados**
- **Agent 3 (User Registry)**: Usar nova estrutura unificada
- **Agent 4 (Session Manager)**: FKs atualizadas para CPF
- **Agent 7 (Persistence)**: Schema completo das 16 tabelas
- **Agent 8 (Schema Generator)**: Script de migração automática

---

## ✅ CRITÉRIOS DE SUCESSO

### **Funcionalidade**
- ✅ Todas as 16 tabelas conectadas ao sistema principal
- ✅ CPF como identificador único funcional
- ✅ Relacionamentos íntegros e funcionais
- ✅ Dados migrados sem perda

### **Performance**
- ✅ Queries otimizadas com novos relacionamentos
- ✅ Índices atualizados para CPF
- ✅ Tempo de resposta mantido ou melhorado

### **Compatibilidade**
- ✅ Sistema legado funcional durante transição
- ✅ APIs atualizadas para novo schema
- ✅ Workflows n8n funcionais com nova estrutura

---

**Status**: Plano de reorganização completo criado  
**Próximo passo**: Implementar migração em ambiente de produção  
**Impacto**: Unificação completa do sistema de dados
