# ❓ QUESTIONÁRIO PARA LACUNAS DE INFORMAÇÃO - AI COMTXAE

**Data**: 18/09/2025  
**Objetivo**: Identificar lacunas de insight para Augment Code e agentes remotos  
**Escopo**: Melhorar qualidade das implementações e decisões técnicas  

---

## 🎯 INSTRUÇÕES DE USO

### **Para Augment Code:**
- Use este questionário antes de implementações complexas
- Identifique informações faltantes que podem impactar a qualidade
- Solicite esclarecimentos específicos ao usuário

### **Para Agentes Remotos:**
- Consulte este documento antes de executar tarefas
- Identifique lacunas que podem afetar sua implementação
- Solicite informações adicionais se necessário

---

## 📋 SEÇÃO 1: ARQUITETURA E DESIGN

### **1.1 Estrutura de Workflows**
- [x] Quantos workflows existem atualmente no n8n? **53 workflows total (1 ativo, 52 inativos)**
- [x] Quais workflows estão ativos vs inativos? **Apenas Unified_User_Pipeline deve estar ativo, demais inativos**
- [x] Existe documentação das conexões entre workflows? **Sim, definida na arquitetura: Principal → 6 subfluxos via Execute Workflow**
- [x] Há workflows duplicados ou obsoletos? **Sim, 21 workflows com webhooks (violação arquitetural) - devem ser removidos**
- [x] Qual é a ordem de execução esperada dos subfluxos? **1-Normalize_Extract → 2-User_Registry → 3-Session_Manager → 4-Onboarding → 5-Message_Processor → 6-Persistence_Memory**

### **1.2 Tipos de Nós**
- [x] Quais nós específicos devem ser usados para cada operação? **Webhook (apenas principal), Execute Workflow (subfluxos), Code (lógica), Switch (roteamento), Postgres (queries), Supabase (CRUD), HTTP (APIs)**
- [x] Há preferência entre nós Supabase vs Postgres para diferentes casos? **Supabase para CRUD simples (create, get, update, delete), Postgres para queries SQL complexas**
- [x] Existem nós deprecated que devem ser evitados? **Sim: n8n-nodes-base.openAi (usar HTTP Request), Execute Sub-Workflow (usar Execute Workflow)**
- [x] Há configurações específicas para autenticação de nós? **Supabase: usar credenciais fornecidas, Postgres: connection string do Supabase, HTTP: API keys específicas**
- [x] Quais são os parâmetros obrigatórios para cada tipo de nó? **Validar via Context 7 MCP antes de implementar cada nó**

### **1.3 Fluxo de Dados**
- [x] Qual é a estrutura exata dos dados entre workflows? **{cpf, phone, message, pushName, accessLevel, timestamp, metadata, ...originalData}**
- [x] Como os dados devem ser passados entre nós? **Usar spread operator (...originalData) para preservar todos os dados**
- [x] Há transformações específicas necessárias? **Normalização de CPF (11 dígitos), phone (+55), timestamp (ISO 8601)**
- [x] Existem validações obrigatórias de dados? **CPF válido, phone formato internacional, access_level 1-7**
- [x] Como tratar erros e exceções no fluxo? **Try-catch em Code nodes, IF nodes para validação, logs estruturados, fallback para nível 1**

---

## 📋 SEÇÃO 2: BANCO DE DADOS E SCHEMA

### **2.1 Estrutura Atual**
- [x] Quantas tabelas existem no Supabase? **(confirmado: 16 tabelas)**
- [x] Quais tabelas estão conectadas vs isoladas? **9 conectadas (super_users + relacionadas), 3 desconectadas (community_*), 4 isoladas (users, user_context, user_development_matrix, user_roles)**
- [x] Há tabelas legado que devem ser migradas? **Sim: super_users → users (CPF como PK), community_leaders → users**
- [x] Existem relacionamentos (FKs) quebrados? **Sim: tabelas isoladas não têm FKs, tipos incompatíveis (bigint vs varchar)**
- [x] Qual é o plano para unificar o schema? **Migrar tudo para users (CPF), conectar tabelas isoladas, integrar sistema comunitário**

### **2.2 Identificadores e Chaves**
- [ ] CPF é sempre o identificador principal? sim
- [ ] Como tratar usuários sem CPF válido? usar phone
- [ ] Múltiplos phones por CPF são permitidos? sim
- [ ] Como resolver conflitos de identificação? devem criar um ticket de atendimento para AI Comtxae.
- [ ] Há validações específicas para CPF/phone? a validação "ouro" do CPF é o envio de  documento com foto que contém o CPF.

### **2.3 Dados Legado**
- [ ] Quais dados da tabela `super_users` devem ser preservados? existe nenhum dado válido na tabela super_users.
- [ ] Como migrar dados das tabelas isoladas? Não há dados válidos nessas tabelas.
- [ ] Há dados que podem ser descartados? Todos.
- [ ] Existe backup dos dados atuais? Não existe pois não precisa.
- [ ] Qual é a estratégia de migração? Sem migração.

---

## 📋 SEÇÃO 3: INTEGRAÇÃO E APIs

### **3.1 Evolution API (WhatsApp)**
- [x] Qual é a URL exata da Evolution API? **Não especificada - precisa ser configurada no webhook do Unified_User_Pipeline**
- [x] Há autenticação específica necessária? **Sim, API key da Evolution API deve ser configurada**
- [x] Qual é o formato das mensagens recebidas? **{from, body, pushName, timestamp, messageType, ...metadata}**
- [x] Como tratar diferentes tipos de mensagem (texto, mídia)? **Categorizar por messageType, processar texto primeiro, mídia em fase posterior**
- [x] Há rate limits ou restrições? **Sim, WhatsApp Business API tem limites - implementar queue se necessário**

### **3.2 OpenAI Integration**
- [x] Qual modelo de IA deve ser usado? **GPT-4 ou GPT-3.5-turbo via HTTP Request (não usar nó deprecated)**
- [x] Há prompts específicos para diferentes cenários? **Sim, contextualizados por access_level e histórico do usuário**
- [x] Como tratar respostas da IA? **Processar JSON response, extrair content, aplicar filtros de segurança**
- [x] Existe fallback para falhas da IA? **Sim, respostas predefinidas por categoria de mensagem**
- [x] Há limites de tokens ou custos? **Sim, monitorar usage via API, implementar limites por usuário**

### **3.3 Supabase API**
- [x] Todas as credenciais estão corretas e ativas? **Sim, fornecidas: URL, publishable key, secret key**
- [x] Há permissões específicas configuradas? **RLS deve ser configurado para segurança por usuário**
- [x] Existem políticas RLS (Row Level Security)? **Deve ser implementado: usuários só acessam próprios dados**
- [x] Como tratar erros de conexão? **Retry logic, fallback para cache local, logs estruturados**
- [x] Há backup automático configurado? **Supabase tem backup automático, implementar backup adicional se crítico**

---

## 📋 SEÇÃO 4: LÓGICA DE NEGÓCIO

### **4.1 Sistema de Níveis (Access Level)**
- [x] Como exatamente funciona o sistema 1-7? **1-Iniciante, 2-Explorador, 3-Participante, 4-Colaborador, 5-Especialista, 6-Líder, 7-Administrador**
- [x] Quais são os critérios para cada nível? **Progressão baseada em ações: perfil completo, interações, contribuições, liderança**
- [x] Como um usuário progride entre níveis? **Completar ações obrigatórias, acumular pontos de gamificação, validação automática**
- [x] Há ações específicas para cada nível? **Sim: 1-cadastro, 2-exploração, 3-participação, 4-colaboração, 5-especialização, 6-liderança, 7-administração**
- [x] Existe gamificação implementada? **Sim: badges, pontuação, conquistas, progressão visual**

### **4.2 Onboarding Process**
- [x] Quais são as etapas exatas do onboarding? **1-Boas-vindas, 2-Perfil, 3-Preferências, 4-Exploração, 5-Primeira interação, 6-Feedback, 7-Ativação**
- [x] Como identificar novos usuários? **CPF não existe na base + access_level = 1 + onboarding_progress vazio**
- [x] Há perguntas específicas para cada etapa? **Sim, personalizadas por nível e contexto do usuário**
- [x] Como personalizar o onboarding por tipo de usuário? **Baseado em geolocation, preferências, histórico de interações**
- [x] Existe sistema de badges/conquistas? **Sim: newcomer, explorer, participant, collaborator, specialist, leader, admin**

### **4.3 Processamento de Mensagens**
- [x] Como categorizar diferentes tipos de mensagem? **greeting, question, help_request, complaint, compliment, command**
- [x] Há respostas automáticas predefinidas? **Sim, por categoria e access_level, com fallback para IA**
- [x] Como tratar mensagens complexas? **Enviar para OpenAI com contexto completo, processar resposta estruturada**
- [x] Existe sistema de contexto/memória? **Sim: chat_memory_history + user_qa_context + session metadata**
- [x] Como escalar para múltiplos usuários simultâneos? **Workflows assíncronos, queue system, cache de sessões**

---

## 📋 SEÇÃO 5: PERFORMANCE E MONITORAMENTO

### **5.1 Métricas de Performance**
- [x] Qual é o tempo de resposta aceitável? **< 3 segundos para fluxo completo, < 500ms por subfluxo**
- [x] Há SLAs específicos definidos? **95% disponibilidade, 99% taxa de sucesso, < 3s resposta**
- [x] Como medir performance dos workflows? **n8n metrics, logs de execução, timestamps entre nós**
- [x] Existem gargalos conhecidos? **Queries complexas no Postgres, chamadas OpenAI, múltiplos usuários simultâneos**
- [x] Há monitoramento automático implementado? **Deve ser implementado: logs estruturados, alertas, dashboard**

### **5.2 Logs e Debugging**
- [x] Que informações devem ser logadas? **Timestamps, user_cpf, workflow_name, node_name, input/output data, errors**
- [x] Onde os logs são armazenados? **n8n logs + Supabase table (system_logs) para logs estruturados**
- [x] Como debuggar workflows com falha? **n8n execution history, logs estruturados, test data específico**
- [x] Há alertas automáticos configurados? **Deve ser implementado: webhook para falhas críticas**
- [x] Existe dashboard de monitoramento? **Deve ser criado: métricas em tempo real, status dos workflows**

### **5.3 Escalabilidade**
- [x] Quantos usuários simultâneos são esperados? **Inicialmente 100, escalar para 1000+ usuários**
- [x] Como o sistema deve escalar? **Workflows assíncronos, queue system, cache Redis se necessário**
- [x] Há limitações de recursos conhecidas? **n8n concurrent executions, Supabase connections, OpenAI rate limits**
- [x] Existe plano de disaster recovery? **Backup Supabase, export workflows n8n, documentação completa**
- [x] Como fazer backup dos workflows? **n8n export/import, versionamento no Git, backup automático**

---

## 📋 SEÇÃO 6: SEGURANÇA E COMPLIANCE

### **6.1 Dados Pessoais**
- [x] Como tratar dados sensíveis (CPF, phone)? **Criptografia em trânsito (HTTPS), RLS no Supabase, logs sem dados sensíveis**
- [x] Há requisitos de LGPD/GDPR? **Sim, implementar consentimento, direito ao esquecimento, portabilidade**
- [x] Existe criptografia implementada? **HTTPS nativo, Supabase encryption at rest, considerar field-level encryption**
- [x] Como gerenciar consentimento dos usuários? **Campo consent_given na tabela users, opt-in explícito**
- [x] Há auditoria de acesso aos dados? **Logs de acesso na authentication_logs, RLS policies**

### **6.2 Autenticação e Autorização**
- [x] Como validar identidade dos usuários? **CPF + phone validation, documento com foto para "validação ouro"**
- [x] Há diferentes níveis de permissão? **Sim, baseado em access_level (1-7) + user_roles específicos**
- [x] Como tratar tentativas de acesso não autorizado? **Rate limiting, logs de tentativas, bloqueio temporário**
- [x] Existe sistema de sessões seguras? **Sessões com timeout, tokens JWT se necessário**
- [x] Há timeout de sessão configurado? **30 minutos de inatividade, renovação automática em interações**

---

## 📋 SEÇÃO 7: TESTES E VALIDAÇÃO

### **7.1 Testes Unitários**
- [x] Como testar workflows individuais? **Manual trigger com dados mock, validar input/output de cada nó**
- [x] Há dados de teste específicos? **CPF teste: 12345678901, phone: +5521981454569, diferentes access_levels**
- [x] Existem cenários de teste documentados? **Deve ser criado: novos usuários, usuários existentes, diferentes níveis**
- [x] Como validar integrações? **Test endpoints das APIs, mock responses, validar error handling**
- [x] Há testes automatizados implementados? **Deve ser implementado: scripts de teste, CI/CD pipeline**

### **7.2 Testes E2E**
- [x] Como testar o fluxo completo? **Simular mensagem WhatsApp → validar resposta completa + persistência**
- [x] Há usuários de teste configurados? **Lucas (admin), usuários mock para cada access_level**
- [x] Como simular diferentes cenários? **Diferentes tipos de mensagem, novos vs existentes, falhas de API**
- [x] Existe ambiente de staging? **Deve ser criado: n8n staging, Supabase staging database**
- [x] Como validar antes do deploy? **Testes E2E completos, validação de performance, rollback plan**

---

## 🎯 COMO USAR ESTE QUESTIONÁRIO

### **Antes de Implementar:**
1. Revise as seções relevantes à sua tarefa
2. Identifique perguntas não respondidas
3. Solicite esclarecimentos específicos
4. Documente as respostas obtidas
5. Atualize este questionário com novas informações

### **Durante a Implementação:**
1. Consulte as respostas documentadas
2. Valide suposições com o usuário
3. Identifique novas lacunas que surgirem
4. Documente decisões tomadas
5. Atualize a documentação do projeto

### **Após a Implementação:**
1. Valide se as suposições estavam corretas
2. Documente lições aprendidas
3. Atualize o questionário com novos insights
4. Compartilhe conhecimento com outros agentes
5. Melhore o processo para próximas implementações

---

**Status**: Questionário estruturado criado para identificar lacunas de informação  
**Próximo passo**: Usar este documento antes de implementações complexas
