#!/usr/bin/env node

/**
 * Deactivate Webhook Workflows Script
 * 
 * Este script desativa todos os workflows que possuem webhooks,
 * exceto o Unified_User_Pipeline, para corrigir a violação arquitetural
 * de múltiplos pontos de entrada.
 * 
 * Usage: node automation/maintenance/deactivate_webhook_workflows.js
 */

require('dotenv').config();
const N8nApiClient = require('./n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host/',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

// Workflow principal que DEVE manter o webhook
const MAIN_WORKFLOW = 'Unified_User_Pipeline';

// Workflows conhecidos com webhooks que devem ser desativados
const WEBHOOK_WORKFLOWS_TO_DEACTIVATE = [
  '02_Agente_Inquisidor_Gamificado',
  '05_Matrix_Orchestrator', 
  'assistente_v0.4',
  'assistente_v0.2_renomeando',
  '04_Matrix_Skills_Collector',
  'assistente_v0.1',
  '01_Matrix_Database_Schema_Generator',
  'assistente_v0.3_Refactoring',
  'WhatsApp_Message_Router.json',
  'assistente_v0.5',
  'Conversational Interviews with AI Agents and n8n Forms',
  'assistente_v0.7',
  'Atendimento WhatsApp v0.1',
  'RAG e Agendamento',
  'assistente_v0.6',
  'WhatsApp Super Usuario v0.2',
  'Vendedor de Produtos',
  '01_WPP_MSG_Router',
  'Agente SDR',
  '03_Matrix_Timeline_Collector'
];

class WebhookWorkflowDeactivator {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.results = {
      timestamp: new Date().toISOString(),
      totalWorkflows: 0,
      webhookWorkflows: [],
      deactivated: [],
      skipped: [],
      errors: []
    };
  }

  // Main deactivation process
  async deactivateWebhookWorkflows() {
    try {
      console.log('🔧 WEBHOOK WORKFLOW DEACTIVATOR - Iniciando processo...\n');
      console.log('🎯 Objetivo: Manter apenas webhook no Unified_User_Pipeline');
      console.log('🔍 Instância n8n:', CONFIG.n8nApiUrl);
      console.log('');

      // Get all workflows
      const workflows = await this.client.getWorkflows();
      this.results.totalWorkflows = workflows.data.length;

      console.log(`📊 Total de workflows encontrados: ${workflows.data.length}`);
      console.log('');

      // Identify workflows with webhooks
      await this.identifyWebhookWorkflows(workflows.data);

      // Deactivate webhook workflows (except main)
      await this.performDeactivation();

      // Display results
      this.displayResults();

      // Save deactivation report
      await this.saveDeactivationReport();

      return this.results;

    } catch (error) {
      console.error('❌ Erro no processo de desativação:', error.message);
      throw error;
    }
  }

  // Identify workflows that have webhooks
  async identifyWebhookWorkflows(workflows) {
    console.log('🔍 IDENTIFICANDO WORKFLOWS COM WEBHOOKS:\n');

    for (const workflow of workflows) {
      try {
        // Get workflow details to check for webhook nodes
        const details = await this.client.getWorkflow(workflow.id);
        
        const webhookNodes = details.nodes.filter(node => 
          node.type === 'n8n-nodes-base.webhook' || 
          node.type === 'n8n-nodes-base.webhookTrigger'
        );

        if (webhookNodes.length > 0) {
          this.results.webhookWorkflows.push({
            id: workflow.id,
            name: workflow.name,
            active: workflow.active,
            webhookCount: webhookNodes.length
          });

          const status = workflow.active ? '✅ ATIVO' : '⚠️ INATIVO';
          console.log(`📋 ${workflow.name} - ${status} - ${webhookNodes.length} webhook(s)`);
        }

      } catch (error) {
        console.log(`❌ Erro ao analisar ${workflow.name}: ${error.message}`);
        this.results.errors.push({
          workflow: workflow.name,
          error: error.message,
          action: 'identify'
        });
      }
    }

    console.log(`\n📊 Workflows com webhooks encontrados: ${this.results.webhookWorkflows.length}`);
    console.log('');
  }

  // Perform the actual deactivation
  async performDeactivation() {
    console.log('🔧 EXECUTANDO DESATIVAÇÃO:\n');

    for (const webhookWorkflow of this.results.webhookWorkflows) {
      try {
        // Skip the main workflow (should keep its webhook)
        if (webhookWorkflow.name === MAIN_WORKFLOW) {
          console.log(`✅ MANTENDO: ${webhookWorkflow.name} (workflow principal)`);
          this.results.skipped.push({
            name: webhookWorkflow.name,
            reason: 'Workflow principal - deve manter webhook'
          });
          continue;
        }

        // Skip if already inactive
        if (!webhookWorkflow.active) {
          console.log(`⚠️ JÁ INATIVO: ${webhookWorkflow.name}`);
          this.results.skipped.push({
            name: webhookWorkflow.name,
            reason: 'Já estava inativo'
          });
          continue;
        }

        // Deactivate the workflow
        console.log(`🔄 DESATIVANDO: ${webhookWorkflow.name}...`);
        
        await this.client.deactivateWorkflow(webhookWorkflow.id);
        
        console.log(`✅ DESATIVADO: ${webhookWorkflow.name}`);
        this.results.deactivated.push({
          name: webhookWorkflow.name,
          id: webhookWorkflow.id,
          webhookCount: webhookWorkflow.webhookCount
        });

        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        console.log(`❌ ERRO ao desativar ${webhookWorkflow.name}: ${error.message}`);
        this.results.errors.push({
          workflow: webhookWorkflow.name,
          error: error.message,
          action: 'deactivate'
        });
      }
    }
  }

  // Display deactivation results
  displayResults() {
    console.log('\n📋 RESUMO DA DESATIVAÇÃO:\n');

    console.log(`📊 Workflows com webhooks encontrados: ${this.results.webhookWorkflows.length}`);
    console.log(`✅ Workflows desativados: ${this.results.deactivated.length}`);
    console.log(`⚠️ Workflows ignorados: ${this.results.skipped.length}`);
    console.log(`❌ Erros encontrados: ${this.results.errors.length}`);

    if (this.results.deactivated.length > 0) {
      console.log('\n✅ WORKFLOWS DESATIVADOS:');
      this.results.deactivated.forEach((workflow, index) => {
        console.log(`${index + 1}. ${workflow.name} (${workflow.webhookCount} webhook(s))`);
      });
    }

    if (this.results.skipped.length > 0) {
      console.log('\n⚠️ WORKFLOWS IGNORADOS:');
      this.results.skipped.forEach((workflow, index) => {
        console.log(`${index + 1}. ${workflow.name} - ${workflow.reason}`);
      });
    }

    if (this.results.errors.length > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:');
      this.results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.workflow}: ${error.error}`);
      });
    }

    // Final status
    const success = this.results.errors.length === 0;
    const webhooksRemaining = this.results.webhookWorkflows.length - this.results.deactivated.length;
    
    console.log('\n🎯 STATUS FINAL:');
    if (success && webhooksRemaining === 1) {
      console.log('✅ SUCESSO: Apenas Unified_User_Pipeline mantém webhook ativo');
      console.log('🏗️ ARQUITETURA: Agora conforme com padrão Unified_User_Pipeline');
    } else if (success) {
      console.log(`⚠️ PARCIAL: ${webhooksRemaining} workflow(s) ainda com webhook`);
      console.log('🔍 RECOMENDAÇÃO: Verificar workflows restantes manualmente');
    } else {
      console.log('❌ FALHA: Erros durante o processo de desativação');
      console.log('🔧 AÇÃO: Verificar erros e executar novamente');
    }
  }

  // Save deactivation report
  async saveDeactivationReport() {
    const fs = require('fs').promises;
    const path = require('path');
    
    const reportPath = path.join(__dirname, '../../docs/architecture/webhook_deactivation_report.json');
    
    try {
      await fs.writeFile(reportPath, JSON.stringify(this.results, null, 2), 'utf8');
      console.log(`\n📄 Relatório salvo em: ${reportPath}`);
    } catch (error) {
      console.error('❌ Erro ao salvar relatório:', error.message);
    }
  }
}

// Main execution
async function main() {
  try {
    const deactivator = new WebhookWorkflowDeactivator();
    await deactivator.deactivateWebhookWorkflows();
    
    console.log('\n🎉 Processo de desativação concluído!');
    console.log('🔍 Execute o Architecture Validator para verificar conformidade:');
    console.log('   node automation/maintenance/architecture_validator.js');
    
  } catch (error) {
    console.error('❌ Falha no processo de desativação:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = WebhookWorkflowDeactivator;
