#!/usr/bin/env node

/**
 * n8n Workflow Configuration Fix Script
 * 
 * This script identifies and fixes common configuration issues in deployed n8n workflows:
 * 1. Missing or unsupported node types
 * 2. Evolution API configuration issues
 * 3. OpenAI credential configuration
 * 4. Environment variable references
 * 
 * Usage: node automation/fix_workflow_configurations.js
 */

require('dotenv').config();
const N8nApiClient = require('./n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'http://localhost:5678',
  n8nApiKey: process.env.N8N_API_KEY,
  evolutionApiUrl: process.env.EVOLUTION_API_URL,
  evolutionApiKey: process.env.EVOLUTION_API_KEY,
  evolutionInstance: process.env.EVOLUTION_INSTANCE,
  openaiApiKey: process.env.OPENAI_API_KEY
};

// Node type mappings for unsupported nodes
const NODE_TYPE_FIXES = {
  // Custom nodes that might not be available
  'n8n-nodes-base.openAi': 'n8n-nodes-base.httpRequest',
  'n8n-nodes-base.supabase': 'n8n-nodes-base.httpRequest',
  'n8n-nodes-base.vectorStore': 'n8n-nodes-base.httpRequest',
  // Add more mappings as needed
};

// Evolution API endpoint configurations
const EVOLUTION_API_ENDPOINTS = {
  sendText: '/message/sendText',
  sendMedia: '/message/sendMedia',
  getInstance: '/instance/fetchInstances',
  createInstance: '/instance/create',
  webhook: '/webhook/set'
};

class WorkflowConfigurationFixer {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.fixLog = [];
  }

  // Main fix process
  async fixAllWorkflows() {
    console.log('🔧 Starting Workflow Configuration Fix Process\n');
    console.log('🎯 Target n8n Instance:', CONFIG.n8nApiUrl);
    console.log('🌐 Evolution API URL:', CONFIG.evolutionApiUrl);
    console.log('🤖 Evolution Instance:', CONFIG.evolutionInstance);
    console.log('');

    try {
      // Step 1: Get all workflows
      const workflows = await this.client.getWorkflows();
      console.log(`📋 Found ${workflows.length} workflows to check\n`);

      // Step 2: Fix each workflow
      for (const workflow of workflows) {
        await this.fixWorkflow(workflow);
      }

      // Step 3: Configure credentials
      await this.configureCredentials();

      // Step 4: Generate report
      await this.generateFixReport();

      console.log('\n✅ Workflow configuration fix completed successfully!');
      return true;

    } catch (error) {
      console.error('❌ Fix process failed:', error.message);
      return false;
    }
  }

  // Fix individual workflow
  async fixWorkflow(workflow) {
    console.log(`🔍 Checking workflow: ${workflow.name}`);
    
    try {
      // Get full workflow details
      const fullWorkflow = await this.client.getWorkflow(workflow.id);
      let hasChanges = false;
      const fixes = [];

      // Fix nodes
      for (let node of fullWorkflow.nodes) {
        const nodeFixes = await this.fixNode(node, workflow.name);
        if (nodeFixes.length > 0) {
          hasChanges = true;
          fixes.push(...nodeFixes);
        }
      }

      // Update workflow if changes were made
      if (hasChanges) {
        await this.client.updateWorkflow(workflow.id, fullWorkflow);
        console.log(`  ✅ Applied ${fixes.length} fixes to ${workflow.name}`);
        
        this.fixLog.push({
          workflow: workflow.name,
          workflowId: workflow.id,
          fixes: fixes,
          status: 'FIXED'
        });
      } else {
        console.log(`  ✓ No fixes needed for ${workflow.name}`);
        
        this.fixLog.push({
          workflow: workflow.name,
          workflowId: workflow.id,
          fixes: [],
          status: 'OK'
        });
      }

    } catch (error) {
      console.error(`  ❌ Failed to fix ${workflow.name}:`, error.message);
      
      this.fixLog.push({
        workflow: workflow.name,
        workflowId: workflow.id,
        fixes: [],
        status: 'ERROR',
        error: error.message
      });
    }
  }

  // Fix individual node
  async fixNode(node, workflowName) {
    const fixes = [];

    // Fix 1: Replace unsupported node types
    if (NODE_TYPE_FIXES[node.type]) {
      const oldType = node.type;
      node.type = NODE_TYPE_FIXES[oldType];
      fixes.push(`Replaced node type ${oldType} with ${node.type}`);
    }

    // Fix 2: Update Evolution API URLs
    if (node.parameters && node.parameters.url) {
      const updatedUrl = this.fixEvolutionApiUrl(node.parameters.url);
      if (updatedUrl !== node.parameters.url) {
        node.parameters.url = updatedUrl;
        fixes.push(`Updated Evolution API URL in node ${node.name}`);
      }
    }

    // Fix 3: Fix Evolution API authentication
    if (this.isEvolutionApiNode(node)) {
      const authFixes = this.fixEvolutionApiAuth(node);
      fixes.push(...authFixes);
    }

    // Fix 4: Fix OpenAI nodes
    if (this.isOpenAiNode(node)) {
      const openAiFixes = this.fixOpenAiNode(node);
      fixes.push(...openAiFixes);
    }

    // Fix 5: Fix environment variable references
    const envFixes = this.fixEnvironmentVariables(node);
    fixes.push(...envFixes);

    return fixes;
  }

  // Fix Evolution API URL
  fixEvolutionApiUrl(url) {
    if (!url || typeof url !== 'string') return url;

    // Replace hardcoded URLs with environment variable
    if (url.includes('evolution') && !url.includes('$env.EVOLUTION_API_URL')) {
      // Extract the endpoint part
      const urlParts = url.split('/');
      const endpointIndex = urlParts.findIndex(part => 
        part === 'message' || part === 'instance' || part === 'webhook'
      );
      
      if (endpointIndex > 0) {
        const endpoint = '/' + urlParts.slice(endpointIndex).join('/');
        return `{{ $env.EVOLUTION_API_URL }}${endpoint}`;
      }
    }

    return url;
  }

  // Check if node is Evolution API related
  isEvolutionApiNode(node) {
    if (!node.parameters) return false;
    
    const url = node.parameters.url;
    return url && (
      url.includes('evolution') || 
      url.includes('$env.EVOLUTION_API_URL') ||
      url.includes('sendText') ||
      url.includes('sendMedia')
    );
  }

  // Fix Evolution API authentication
  fixEvolutionApiAuth(node) {
    const fixes = [];

    if (!node.parameters) return fixes;

    // Ensure proper authentication is set
    if (!node.parameters.authentication || node.parameters.authentication !== 'genericCredentialType') {
      node.parameters.authentication = 'genericCredentialType';
      node.parameters.genericAuthType = 'httpHeaderAuth';
      fixes.push(`Set authentication type for ${node.name}`);
    }

    // Fix header authentication
    if (!node.parameters.httpHeaderAuth) {
      node.parameters.httpHeaderAuth = {
        name: 'apikey',
        value: '={{ $env.EVOLUTION_API_KEY }}'
      };
      fixes.push(`Set Evolution API key header for ${node.name}`);
    }

    return fixes;
  }

  // Check if node is OpenAI related
  isOpenAiNode(node) {
    return node.type && (
      node.type.includes('openAi') || 
      node.type.includes('openai') ||
      (node.parameters && node.parameters.model && node.parameters.model.includes('gpt'))
    );
  }

  // Fix OpenAI node configuration
  fixOpenAiNode(node) {
    const fixes = [];

    // Convert OpenAI node to HTTP Request if needed
    if (node.type === 'n8n-nodes-base.openAi' || !this.isNodeTypeAvailable(node.type)) {
      node.type = 'n8n-nodes-base.httpRequest';
      node.parameters = {
        url: 'https://api.openai.com/v1/chat/completions',
        authentication: 'genericCredentialType',
        genericAuthType: 'httpHeaderAuth',
        httpHeaderAuth: {
          name: 'Authorization',
          value: 'Bearer {{ $env.OPENAI_API_KEY }}'
        },
        sendBody: true,
        specifyBody: 'json',
        jsonBody: JSON.stringify({
          model: '{{ $env.OPENAI_MODEL || "gpt-3.5-turbo" }}',
          messages: [
            {
              role: 'user',
              content: '{{ $json.message || $json.prompt }}'
            }
          ],
          max_tokens: '{{ $env.OPENAI_MAX_TOKENS || 1000 }}',
          temperature: '{{ $env.OPENAI_TEMPERATURE || 0.7 }}'
        }, null, 2),
        options: {
          timeout: 30000
        }
      };
      fixes.push(`Converted OpenAI node ${node.name} to HTTP Request`);
    }

    return fixes;
  }

  // Fix environment variable references
  fixEnvironmentVariables(node) {
    const fixes = [];

    if (!node.parameters) return fixes;

    // Convert string parameters to check for environment variables
    const paramStr = JSON.stringify(node.parameters);
    let updatedParamStr = paramStr;

    // Fix common environment variable patterns
    const envVarFixes = [
      { pattern: /\$env\.EVOLUTION_API_URL/g, replacement: '{{ $env.EVOLUTION_API_URL }}' },
      { pattern: /\$env\.EVOLUTION_API_KEY/g, replacement: '{{ $env.EVOLUTION_API_KEY }}' },
      { pattern: /\$env\.EVOLUTION_INSTANCE/g, replacement: '{{ $env.EVOLUTION_INSTANCE }}' },
      { pattern: /\$env\.OPENAI_API_KEY/g, replacement: '{{ $env.OPENAI_API_KEY }}' }
    ];

    for (const fix of envVarFixes) {
      if (fix.pattern.test(updatedParamStr)) {
        updatedParamStr = updatedParamStr.replace(fix.pattern, fix.replacement);
        fixes.push(`Fixed environment variable syntax in ${node.name}`);
      }
    }

    // Update parameters if changes were made
    if (updatedParamStr !== paramStr) {
      try {
        node.parameters = JSON.parse(updatedParamStr);
      } catch (error) {
        console.warn(`Warning: Could not parse updated parameters for ${node.name}`);
      }
    }

    return fixes;
  }

  // Check if node type is available (simplified check)
  isNodeTypeAvailable(nodeType) {
    // List of commonly available node types
    const availableTypes = [
      'n8n-nodes-base.httpRequest',
      'n8n-nodes-base.webhook',
      'n8n-nodes-base.code',
      'n8n-nodes-base.if',
      'n8n-nodes-base.set',
      'n8n-nodes-base.executeWorkflow',
      'n8n-nodes-base.executeWorkflowTrigger',
      'n8n-nodes-base.respondToWebhook'
    ];

    return availableTypes.includes(nodeType);
  }

  // Configure credentials
  async configureCredentials() {
    console.log('\n🔑 Configuring credentials...');

    try {
      // This would typically involve API calls to set up credentials
      // For now, we'll just log what needs to be done
      console.log('  📝 OpenAI API Key needs to be configured in n8n interface');
      console.log('  📝 Evolution API credentials are configured via environment variables');
      
      this.fixLog.push({
        workflow: 'CREDENTIALS',
        fixes: ['OpenAI credentials need manual configuration'],
        status: 'MANUAL_ACTION_REQUIRED'
      });

    } catch (error) {
      console.error('  ❌ Failed to configure credentials:', error.message);
    }
  }

  // Generate fix report
  async generateFixReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = `workflow_fix_report_${timestamp}.json`;

    const report = {
      timestamp: new Date().toISOString(),
      n8nInstance: CONFIG.n8nApiUrl,
      evolutionApiUrl: CONFIG.evolutionApiUrl,
      totalWorkflows: this.fixLog.filter(log => log.workflow !== 'CREDENTIALS').length,
      fixedWorkflows: this.fixLog.filter(log => log.status === 'FIXED').length,
      errorWorkflows: this.fixLog.filter(log => log.status === 'ERROR').length,
      manualActionsRequired: this.fixLog.filter(log => log.status === 'MANUAL_ACTION_REQUIRED').length,
      details: this.fixLog
    };

    const fs = require('fs').promises;
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log(`\n📊 Fix Report Generated: ${reportPath}`);
    console.log(`   ✅ Fixed: ${report.fixedWorkflows} workflows`);
    console.log(`   ❌ Errors: ${report.errorWorkflows} workflows`);
    console.log(`   ⚠️  Manual Actions: ${report.manualActionsRequired} items`);
  }
}

// Main execution
async function main() {
  if (!CONFIG.n8nApiKey) {
    console.error('❌ N8N_API_KEY environment variable is required');
    process.exit(1);
  }

  const fixer = new WorkflowConfigurationFixer();
  const success = await fixer.fixAllWorkflows();
  
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = WorkflowConfigurationFixer;
