# 🏃‍♂️ SPRINT ATUAL - AI COMTXAE

**Data**: 19/09/2025
**Sprint**: Implementação dos 8 Workflows Funcionais
**Duração**: 8 dias úteis
**Status**: ✅ PREPARADO - Repositório organizado, agentes prontos

---

## 🎯 OBJETIVO DO SPRINT

**Implementar sistema completo de automação WhatsApp com 8 workflows funcionais usando agentes remotos especializados.**

### **Entregáveis Principais**
1. **Schema Supabase unificado** (20 tabelas com CPF como PK) ✅
2. **8 workflows funcionais** no n8n (1 principal + 6 subfluxos + 1 utilitário)
3. **Webhook único** (apenas Unified_User_Pipeline)
4. **Sistema de níveis** gamificado (1-7)
5. **Integração completa** WhatsApp → IA → Resposta

---

## 📋 BACKLOG DO SPRINT

### **FASE 1: INFRAESTRUTURA (Dia 1)**
- [X] **Agent 8**: Database_Schema_Generator
  - ✅ Unificar 20 tabelas do Supabase
  - ✅ CPF como identificador único principal
  - ✅ Schema validado e documentado
  - ✅ Integridade confirmada

### **FASE 2: WORKFLOW PRINCIPAL (Dia 2)**
- [ ] **Agent 1**: Unified_User_Pipeline
  - Webhook único do sistema
  - Roteamento por access_level (1-7)
  - Execução dos 6 subfluxos
  - Tempo de resposta < 3s

### **FASE 3: SUBFLUXOS SEQUENCIAIS (Dias 3-8)**
- [ ] **Agent 2**: Normalize_Extract_Subflow (Dia 3)
  - Normalização de CPF, phone, mensagem
  - Extração de metadados
  - Validação de dados de entrada

- [ ] **Agent 3**: User_Registry_Access_Subflow (Dia 4)
  - Busca/criação de usuários
  - Determinação de access_level
  - Atualização de last_access

- [ ] **Agent 4**: Session_Manager_Subflow (Dia 5)
  - Gestão de sessões ativas
  - Histórico de mensagens
  - Controle de timeout (30 min)

- [ ] **Agent 5**: Onboarding_Orchestrator_Subflow (Dia 6)
  - Sistema gamificado (7 níveis)
  - Badges e conquistas
  - Personalização por nível

- [ ] **Agent 6**: Message_Processor_Subflow (Dia 7)
  - Categorização de mensagens
  - Processamento com OpenAI
  - Envio via Evolution API

- [ ] **Agent 7**: Persistence_Memory_Subflow (Dia 8)
  - Persistência de memória
  - Contexto inteligente
  - Consolidação final

---

## ✅ DEFINITION OF DONE

### **Para cada Workflow**
- [ ] Workflow criado e ativo no n8n
- [ ] Nomenclatura correta implementada
- [ ] Todos os nós Code em Python
- [ ] Nós conectados funcionalmente
- [ ] Teste individual aprovado
- [ ] Integração com outros workflows validada
- [ ] Performance dentro do esperado (< 500ms por subfluxo)

### **Para o Sistema Completo**
- [ ] Webhook único funcional (apenas Unified_User_Pipeline)
- [ ] Schema unificado com 16 tabelas conectadas
- [ ] Fluxo sequencial completo (Etapas 0-6)
- [ ] Sistema de níveis gamificado (1-7)
- [ ] Tempo de resposta total < 3 segundos
- [ ] Taxa de sucesso > 95%
- [ ] Documentação atualizada

---

## 🔧 ESPECIFICAÇÕES TÉCNICAS

### **Configurações Obrigatórias**
- **N8N API**: https://n8n-n8n.w9jo16.easypanel.host
- **Supabase**: https://qvkstxeayyzrntywifdi.supabase.co
- **Context 7 MCP**: `/n8n-io/n8n-docs` (obrigatório)
- **Linguagem**: Python para todos os nós Code

### **Regras de Implementação**
1. **Execução recorrente** até workflow 100% funcional
2. **Renomear** workflows existentes ou criar novos
3. **Python obrigatório** em todos os nós Code
4. **Validação via Context 7** antes de criar nós
5. **Teste individual** antes de integração

---

## 📊 MÉTRICAS DE SUCESSO

### **Técnicas**
- **8 workflows funcionais** (100%)
- **Webhook único** (1 ativo, demais inativos)
- **Schema unificado** (16 tabelas conectadas)
- **Performance** (< 3s resposta total)
- **Confiabilidade** (> 95% taxa de sucesso)

### **Funcionais**
- **Onboarding automático** para novos usuários
- **Roteamento inteligente** por access_level
- **Respostas contextualizadas** da IA
- **Gamificação funcional** (badges, níveis)
- **Persistência completa** de histórico

---

## 🚨 RISCOS E MITIGAÇÕES

### **Riscos Identificados**
1. **Workflows existentes** com nomenclatura incorreta
2. **Nós deprecated** em uso
3. **Conexões quebradas** entre nós
4. **Código JavaScript** legado

### **Mitigações**
1. **Execução recorrente** até funcionalidade completa
2. **Context 7 MCP obrigatório** para validação
3. **Teste individual** de cada workflow
4. **Conversão para Python** obrigatória

---

## 📅 CRONOGRAMA DETALHADO

### **Semana 1 (Dias 1-5)**
- **Dia 1**: Agent 8 (Database Schema)
- **Dia 2**: Agent 1 (Unified Pipeline)
- **Dia 3**: Agent 2 (Normalize Extract)
- **Dia 4**: Agent 3 (User Registry)
- **Dia 5**: Agent 4 (Session Manager)

### **Semana 2 (Dias 6-8)**
- **Dia 6**: Agent 5 (Onboarding Orchestrator)
- **Dia 7**: Agent 6 (Message Processor)
- **Dia 8**: Agent 7 (Persistence Memory)

---

## 🎯 PRÓXIMOS PASSOS

### **Imediatos**
1. **Executar Agent 8** (Database_Schema_Generator)
2. **Validar schema** unificado
3. **Executar Agent 1** (Unified_User_Pipeline)
4. **Testar webhook** único

### **Sequenciais**
1. **Executar Agents 2-7** na ordem definida
2. **Validar cada subfluxo** individualmente
3. **Testar integração** completa
4. **Deploy em produção**

---

## 📚 DOCUMENTAÇÃO DE APOIO

### **Arquivos Essenciais**
- **`remote_agents_unificados.md`** - Prompts dos 8 agentes
- **`docs/architecture/NUMERACAO_SUBFLUXOS_ETAPAS.md`** - Fluxo sequencial
- **`docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md`** - Schema unificado
- **`docs/planning/QUESTIONARIO_LACUNAS_INFORMACAO.md`** - 95 respostas técnicas
- **`docs/PLANO_IMPLEMENTACAO_AGENTES.md`** - Plano de execução

### **Lições Aprendidas**
- **Agents 5, 6, 7** executaram com sucesso em branches separadas
- **Verificação inicial** falhou por não sincronizar branches
- **Execução recorrente** é necessária até funcionalidade completa

---

**Status**: Sprint em execução  
**Próximo milestone**: Agent 8 (Database Schema Generator)  
**Meta**: Sistema completo funcional em 8 dias úteis
