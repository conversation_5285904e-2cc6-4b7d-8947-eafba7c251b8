{"environment": "development", "version": "1.0.0-dev", "last_updated": "2025-08-27T00:00:00.000Z", "n8n": {"base_url": "http://localhost:5678", "webhook_url": "http://localhost:5678/webhook", "execution_mode": "main", "log_level": "debug", "max_execution_timeout": 600, "webhook_timeout": 60, "default_timezone": "America/Sao_Paulo"}, "workflows": {"whatsapp_message_router": {"id": "WhatsApp_Message_Router_v1_Dev", "active": true, "webhook_path": "whatsapp-webhook-dev", "timeout": 60}, "superuser_authentication": {"id": "SuperUser_Authentication_v1_Dev", "active": true, "timeout": 30}, "session_manager": {"id": "Session_Manager_v1_Dev", "active": true, "timeout": 30}, "main_orchestrator": {"id": "Main_Orchestrator_Agent_v2_Dev", "active": true, "timeout": 120}, "message_processor": {"id": "Message_Processor_v1_Dev", "active": true, "timeout": 60}}, "apis": {"evolution_api": {"base_url": "http://localhost:8080", "instance": "dev_instance", "timeout": 60, "retry_attempts": 1, "retry_delay": 500, "mock_responses": true, "endpoints": {"send_text": "/message/sendText/{instance}", "send_media": "/message/sendMedia/{instance}", "instance_status": "/instance/fetchInstances", "webhook_config": "/webhook/set/{instance}"}}, "openai": {"base_url": "https://api.openai.com/v1", "model": "gpt-3.5-turbo", "max_tokens": 500, "temperature": 0.9, "timeout": 120, "retry_attempts": 1, "retry_delay": 1000, "mock_responses": false, "endpoints": {"chat_completions": "/chat/completions", "models": "/models"}}, "supabase": {"base_url": "http://localhost:54321", "rest_endpoint": "/rest/v1", "timeout": 60, "retry_attempts": 1, "retry_delay": 500, "use_local_db": true, "headers": {"Content-Type": "application/json", "Prefer": "return=representation"}}}, "database": {"connection": {"host": "localhost", "port": 54322, "database": "postgres", "schema": "public"}, "tables": {"super_users": {"name": "super_users", "primary_key": "id", "indexes": ["phone", "active"]}, "chat_sessions": {"name": "chat_sessions", "primary_key": "id", "indexes": ["user_id", "channel_type", "channel_identifier", "active"]}, "chat_messages": {"name": "chat_messages", "primary_key": "id", "indexes": ["session_id", "user_id", "created_at", "delivery_status"]}, "documents": {"name": "documents", "primary_key": "id", "indexes": ["document_type", "embedding"]}, "appointments": {"name": "appointments", "primary_key": "id", "indexes": ["user_id", "date", "status"]}, "authentication_logs": {"name": "authentication_logs", "primary_key": "id", "indexes": ["phone", "timestamp", "status", "user_id"]}}}, "security": {"authentication": {"required": true, "method": "database_lookup", "session_timeout": 7200, "max_login_attempts": 10, "lockout_duration": 300}, "rate_limiting": {"enabled": false, "max_requests_per_minute": 1000, "max_requests_per_hour": 10000, "burst_limit": 100}, "logging": {"log_all_requests": true, "log_failed_auth": true, "log_sensitive_data": true, "retention_days": 7}}, "features": {"debug_logging": true, "test_mode": true, "skip_authentication": false, "mock_apis": true, "detailed_error_messages": true, "performance_monitoring": true, "health_checks": true, "auto_reload": true, "hot_reload": true}, "testing": {"enabled": true, "mock_data": {"test_phone_numbers": ["5511999999999", "5511888888888", "5511777777777"], "test_messages": ["Hello, this is a test message", "How are you?", "What can you help me with?"]}, "test_users": [{"phone": "5511999999999", "name": "Test User 1", "access_level": 3, "active": true, "metadata": {"permissions": ["read", "write", "admin"]}}, {"phone": "5511888888888", "name": "Test User 2", "access_level": 1, "active": true, "metadata": {"permissions": ["read"]}}]}, "monitoring": {"health_check_interval": 60, "metrics_collection": true, "alert_thresholds": {"response_time_ms": 10000, "error_rate_percent": 20, "queue_length": 1000}, "notifications": {"webhook_failures": true, "authentication_failures": false, "api_errors": true, "performance_degradation": false}}, "performance": {"max_concurrent_executions": 10, "queue_mode": "main", "execution_timeout": 600, "memory_limit_mb": 1024, "cpu_limit_percent": 100}, "backup": {"enabled": false, "frequency": "manual", "retention_days": 7, "include_credentials": true, "backup_location": "dev_backups/"}, "development": {"auto_save": true, "live_reload": true, "debug_mode": true, "verbose_logging": true, "test_data_reset": true, "mock_external_apis": true, "skip_validations": false}}