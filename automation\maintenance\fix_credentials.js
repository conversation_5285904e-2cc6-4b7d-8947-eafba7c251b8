#!/usr/bin/env node

/**
 * Fix Credential IDs in Product Workflows
 * 
 * This script updates all product workflow files to use the correct credential IDs
 * that exist in the n8n instance.
 */

const fs = require('fs').promises;
const path = require('path');

// Credential ID mappings (from existing n8n instance)
const CREDENTIAL_MAPPINGS = {
  // PostgreSQL/Supabase credentials
  'supabase-connection': 'tqtLhtoqDEUD7G1k',
  'supabase-credentials': 'tqtLhtoqDEUD7G1k',
  'postgres-connection': 'tqtLhtoqDEUD7G1k',
  
  // OpenAI credentials (we'll need to create this or find the correct ID)
  'openai-credentials': 'openai-credentials', // Keep as-is for now, will need to be created
};

async function fixCredentialsInFile(filePath) {
  try {
    console.log(`📝 Processing: ${path.basename(filePath)}`);
    
    // Read the workflow file
    const content = await fs.readFile(filePath, 'utf8');
    let workflowData = JSON.parse(content);
    
    let modified = false;
    
    // Process each node
    if (workflowData.nodes) {
      workflowData.nodes.forEach((node, nodeIndex) => {
        if (node.credentials) {
          Object.keys(node.credentials).forEach(credType => {
            const credConfig = node.credentials[credType];
            if (credConfig.id && CREDENTIAL_MAPPINGS[credConfig.id]) {
              const oldId = credConfig.id;
              const newId = CREDENTIAL_MAPPINGS[credConfig.id];
              
              if (oldId !== newId) {
                console.log(`  🔄 Node "${node.name}": ${oldId} → ${newId}`);
                credConfig.id = newId;
                
                // Update the name to match
                if (newId === 'tqtLhtoqDEUD7G1k') {
                  credConfig.name = 'assistente_v0.1';
                }
                
                modified = true;
              }
            }
          });
        }
      });
    }
    
    if (modified) {
      // Write the updated workflow back to file
      await fs.writeFile(filePath, JSON.stringify(workflowData, null, 2));
      console.log(`  ✅ Updated: ${path.basename(filePath)}`);
    } else {
      console.log(`  ⏭️  No changes needed: ${path.basename(filePath)}`);
    }
    
    return modified;
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

async function fixAllCredentials() {
  console.log('🔧 Fixing Credential IDs in Product Workflows\n');
  
  const productFlowsPath = path.join(__dirname, '../product_flows');
  
  try {
    // Get all JSON files in product_flows directory
    const files = await fs.readdir(productFlowsPath);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    
    console.log(`📁 Found ${jsonFiles.length} workflow files to process\n`);
    
    let totalModified = 0;
    
    // Process each file
    for (const file of jsonFiles) {
      const filePath = path.join(productFlowsPath, file);
      const wasModified = await fixCredentialsInFile(filePath);
      if (wasModified) totalModified++;
    }
    
    console.log(`\n🎉 Credential fix completed!`);
    console.log(`📊 Files modified: ${totalModified}/${jsonFiles.length}`);
    
    if (totalModified > 0) {
      console.log('\n⚠️  Note: OpenAI credentials may still need to be created in n8n');
      console.log('   You can create them manually or update the credential ID after creation.');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  fixAllCredentials();
}

module.exports = { fixAllCredentials, fixCredentialsInFile };
