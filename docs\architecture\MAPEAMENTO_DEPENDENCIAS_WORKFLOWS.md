# 🔗 MAPEAMENTO DE DEPENDÊNCIAS - WORKFLOWS AI COMTXAE

**Data**: 19/09/2025  
**Status**: ✅ **DEPENDÊNCIAS MAPEADAS E VALIDADAS**  
**Objetivo**: Identificar dependências técnicas para evitar falhas de integração  

---

## 🎯 **VISÃO GERAL DAS DEPENDÊNCIAS**

### **TIPOS DE DEPENDÊNCIAS**
1. **Dependência de Dados**: Workflow B precisa dos dados processados por Workflow A
2. **Dependência de Estado**: Workflow B precisa que Workflow A tenha executado com sucesso
3. **Dependência de Configuração**: Workflow B precisa de configurações estabelecidas por Workflow A
4. **Dependência de Infraestrutura**: Workflow B precisa de recursos criados por Workflow A

---

## 🏗️ **MAPA COMPLETO DE DEPENDÊNCIAS**

### **UNIFIED_USER_PIPELINE (Agent 1) - NÍVEL 0**
```
📍 ENTRADA: Webhook WhatsApp
📤 SAÍDA: Dados roteados para subfluxos
🔗 DEPENDÊNCIAS: NENHUMA (ponto de entrada)
📋 DEPENDENTES: TODOS os outros workflows
```

**Função crítica**: 
- Único ponto de entrada do sistema
- Roteamento por access_level (1-7)
- Execução dos 6 subfluxos via Execute Workflow nodes

---

### **NORMALIZE_EXTRACT_SUBFLOW (Agent 2) - NÍVEL 1**
```
📍 ENTRADA: Dados brutos do Unified_User_Pipeline
📤 SAÍDA: Dados normalizados (CPF, phone, mensagem)
🔗 DEPENDÊNCIAS: 
  - Unified_User_Pipeline (Execute Workflow node)
📋 DEPENDENTES: 
  - User_Registry_Access_Subflow
  - Todos os subfluxos subsequentes
```

**Dependências técnicas**:
- **Dados**: CPF bruto, phone bruto, mensagem bruta
- **Estado**: Unified_User_Pipeline deve ter executado
- **Configuração**: Execute Workflow node configurado

---

### **USER_REGISTRY_ACCESS_SUBFLOW (Agent 3) - NÍVEL 2**
```
📍 ENTRADA: Dados normalizados do Normalize_Extract_Subflow
📤 SAÍDA: Usuário identificado/criado + access_level
🔗 DEPENDÊNCIAS:
  - Normalize_Extract_Subflow (dados normalizados)
  - Supabase (tabela users)
📋 DEPENDENTES:
  - Session_Manager_Subflow
  - Todos os subfluxos subsequentes
```

**Dependências técnicas**:
- **Dados**: CPF normalizado, phone normalizado
- **Estado**: Normalize_Extract_Subflow executado com sucesso
- **Infraestrutura**: Conexão Supabase ativa, tabela users acessível
- **Configuração**: Credenciais Supabase configuradas

---

### **SESSION_MANAGER_SUBFLOW (Agent 4) - NÍVEL 3**
```
📍 ENTRADA: Usuário identificado + access_level
📤 SAÍDA: Sessão ativa + contexto do usuário
🔗 DEPENDÊNCIAS:
  - User_Registry_Access_Subflow (usuário identificado)
  - Supabase (tabelas sessions, user_context)
📋 DEPENDENTES:
  - Onboarding_Orchestrator_Subflow
  - Message_Processor_Subflow
  - Persistence_Memory_Subflow
```

**Dependências técnicas**:
- **Dados**: user_id (CPF), access_level, user_data
- **Estado**: User_Registry_Access_Subflow executado
- **Infraestrutura**: Tabelas sessions, user_context disponíveis
- **Configuração**: Políticas RLS configuradas

---

## 🌟 **SUBFLUXOS ESPECIALIZADOS - NÍVEL 4**

### **ONBOARDING_ORCHESTRATOR_SUBFLOW (Agent 5)**
```
📍 ENTRADA: Sessão ativa + usuário novo (access_level 1-3)
📤 SAÍDA: Processo de onboarding executado
🔗 DEPENDÊNCIAS:
  - Session_Manager_Subflow (sessão ativa)
  - Supabase (tabelas onboarding_progress, user_development_matrix)
📋 DEPENDENTES: NENHUM (terminal)
```

**Dependências específicas**:
- **Condição**: access_level 1-3 (usuários em onboarding)
- **Dados**: user_context, current_level, progress_data
- **Infraestrutura**: Tabelas de progresso de onboarding

### **MESSAGE_PROCESSOR_SUBFLOW (Agent 6)**
```
📍 ENTRADA: Sessão ativa + mensagem do usuário
📤 SAÍDA: Resposta processada pela IA
🔗 DEPENDÊNCIAS:
  - Session_Manager_Subflow (contexto da sessão)
  - OpenAI API (processamento de IA)
  - Supabase (tabelas messages, ai_responses)
📋 DEPENDENTES:
  - Persistence_Memory_Subflow (dados para persistir)
```

**Dependências críticas**:
- **Dados**: message_content, user_context, session_data
- **Infraestrutura**: OpenAI API key válida, conexão estável
- **Configuração**: Prompts de IA configurados, rate limits respeitados

### **PERSISTENCE_MEMORY_SUBFLOW (Agent 7)**
```
📍 ENTRADA: Dados processados + resposta da IA
📤 SAÍDA: Dados persistidos + memória atualizada
🔗 DEPENDÊNCIAS:
  - Message_Processor_Subflow (dados processados)
  - Supabase (todas as tabelas de persistência)
📋 DEPENDENTES: NENHUM (terminal)
```

**Dependências de persistência**:
- **Dados**: processed_message, ai_response, user_interaction
- **Infraestrutura**: Todas as 20 tabelas Supabase acessíveis
- **Estado**: Message_Processor_Subflow executado com sucesso

---

### **DATABASE_SCHEMA_GENERATOR (Agent 8) - INDEPENDENTE**
```
📍 ENTRADA: Trigger manual ou agendado
📤 SAÍDA: Schema validado + relatório de integridade
🔗 DEPENDÊNCIAS: 
  - Supabase (acesso administrativo)
📋 DEPENDENTES: NENHUM (utilitário independente)
```

**Características especiais**:
- **Independente**: Não depende de outros workflows
- **Utilitário**: Execução sob demanda
- **Administrativo**: Acesso completo ao schema

---

## ⚠️ **PONTOS CRÍTICOS DE FALHA**

### **DEPENDÊNCIAS CRÍTICAS**
1. **Unified_User_Pipeline → TODOS**: Se falhar, sistema inteiro para
2. **Normalize_Extract_Subflow → Cadeia principal**: Dados corrompidos propagam
3. **User_Registry_Access_Subflow → Identificação**: Sem usuário, sem contexto
4. **Session_Manager_Subflow → Contexto**: Sem sessão, sem continuidade

### **DEPENDÊNCIAS DE INFRAESTRUTURA**
1. **Supabase**: 7 de 8 workflows dependem
2. **OpenAI API**: Message_Processor_Subflow crítico
3. **n8n API**: Todos os workflows (criação/execução)
4. **Execute Workflow nodes**: Integração entre workflows

---

## 🔧 **CONFIGURAÇÕES NECESSÁRIAS**

### **ANTES DE IMPLEMENTAR QUALQUER WORKFLOW**
1. **Supabase**:
   - ✅ 20 tabelas criadas e acessíveis
   - ✅ RLS policies configuradas
   - ✅ Credenciais válidas (URL, keys)

2. **n8n**:
   - ⚠️ API key atualizada (atual expirada)
   - ✅ Cliente API disponível
   - ✅ Instância acessível

3. **OpenAI**:
   - ✅ API key válida
   - ✅ Rate limits conhecidos
   - ✅ Modelos disponíveis

### **CONFIGURAÇÕES POR WORKFLOW**

#### **Unified_User_Pipeline**
- Webhook configurado e ativo
- 6 Execute Workflow nodes criados
- Roteamento por access_level implementado

#### **Subfluxos (2-7)**
- Conexão com workflow anterior
- Credenciais Supabase configuradas
- Tratamento de erros implementado

#### **Database_Schema_Generator**
- Acesso administrativo ao Supabase
- Scripts de validação disponíveis
- Relatórios de integridade configurados

---

## 🧪 **ESTRATÉGIAS DE TESTE**

### **TESTE DE DEPENDÊNCIAS**
1. **Teste individual**: Cada workflow isoladamente
2. **Teste de integração**: Workflows + dependências
3. **Teste de cadeia**: Fluxo completo 1→2→3→4
4. **Teste de falha**: Comportamento quando dependência falha

### **VALIDAÇÃO DE DADOS**
1. **Entrada**: Dados corretos chegando ao workflow
2. **Processamento**: Transformação correta dos dados
3. **Saída**: Dados corretos saindo do workflow
4. **Propagação**: Dados corretos chegando ao próximo workflow

---

## 📋 **CHECKLIST DE IMPLEMENTAÇÃO**

### **ANTES DE IMPLEMENTAR WORKFLOW**
- [ ] Todas as dependências implementadas e funcionais
- [ ] Configurações necessárias validadas
- [ ] Credenciais testadas e funcionais
- [ ] Context 7 consultado para tipos de nós

### **DURANTE A IMPLEMENTAÇÃO**
- [ ] Nós conectados funcionalmente
- [ ] Dados preservados entre nós
- [ ] Tratamento de erros implementado
- [ ] Tempo de resposta < 3 segundos

### **APÓS IMPLEMENTAÇÃO**
- [ ] Teste individual do workflow
- [ ] Teste de integração com dependências
- [ ] Validação de dados entrada/saída
- [ ] Documentação atualizada

---

## 🎯 **CONCLUSÃO**

### **DEPENDÊNCIAS MAPEADAS**
- ✅ **8 workflows** com dependências identificadas
- ✅ **Sequência crítica** estabelecida (1→2→3→4)
- ✅ **Pontos de falha** identificados e documentados
- ✅ **Configurações necessárias** listadas

### **PRÓXIMO PASSO**
Implementar workflows seguindo a sequência de dependências:
1. **Agent 1** (sem dependências)
2. **Agent 2** (depende de 1)
3. **Agent 3** (depende de 2)
4. **Agent 4** (depende de 3)
5. **Agents 5, 6, 7** (dependem de 4, podem ser paralelos)
6. **Agent 8** (independente)

**Status**: ✅ **DEPENDÊNCIAS MAPEADAS - IMPLEMENTAÇÃO PODE PROSSEGUIR**
