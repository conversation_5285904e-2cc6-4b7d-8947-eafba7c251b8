#!/usr/bin/env node

/**
 * Test script para verificar conectividade com n8n API
 * e obter informações sobre o workflow Unified_User_Pipeline
 */

require('dotenv').config();
const N8nApiClient = require('./automation/maintenance/n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host/',
  n8nApiKey: process.env.N8N_API_KEY
};

async function testN8nConnection() {
  console.log('🔍 Testando conectividade com n8n API...');
  console.log('📍 URL:', CONFIG.n8nApiUrl);
  console.log('🔑 API Key:', CONFIG.n8nApiKey ? 'Configurada' : 'NÃO CONFIGURADA');
  console.log('');

  try {
    const client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    
    // Testar conectividade básica
    console.log('📋 Listando workflows...');
    const workflows = await client.getWorkflows();

    // Debug: verificar o tipo e conteúdo da resposta
    console.log('🔍 Debug - Tipo da resposta:', typeof workflows);
    console.log('🔍 Debug - É array?', Array.isArray(workflows));
    console.log('🔍 Debug - Conteúdo (primeiros 200 chars):', JSON.stringify(workflows).substring(0, 200));

    // Verificar se é um array ou se tem propriedade data
    let workflowList = workflows;
    if (workflows && workflows.data && Array.isArray(workflows.data)) {
      workflowList = workflows.data;
    } else if (!Array.isArray(workflows)) {
      console.log('❌ Resposta não é um array. Estrutura:', Object.keys(workflows || {}));
      return;
    }

    console.log(`✅ Conectividade OK! Encontrados ${workflowList.length} workflows`);
    console.log('');

    // Buscar o workflow Unified_User_Pipeline
    console.log('🎯 Buscando Unified_User_Pipeline...');
    const unifiedPipeline = workflowList.find(w => w.name === 'Unified_User_Pipeline');

    // Buscar também por ID específico mencionado na documentação
    console.log('🔍 Buscando por ID ybV3msRSWJcVjxoA...');
    const pipelineById = workflowList.find(w => w.id === 'ybV3msRSWJcVjxoA');

    // Buscar por nomes similares
    console.log('🔍 Buscando workflows com "Pipeline" ou "Unified" no nome...');
    const similarWorkflows = workflowList.filter(w =>
      w.name.toLowerCase().includes('pipeline') ||
      w.name.toLowerCase().includes('unified') ||
      w.name.toLowerCase().includes('user')
    );

    if (similarWorkflows.length > 0) {
      console.log('📋 Workflows similares encontrados:');
      similarWorkflows.forEach((w, index) => {
        console.log(`   ${index + 1}. ${w.name} (ID: ${w.id}) - ${w.active ? 'ATIVO' : 'INATIVO'}`);
      });
      console.log('');
    }

    const targetWorkflow = unifiedPipeline || pipelineById;

    if (targetWorkflow) {
      console.log('✅ Workflow encontrado!');
      console.log('📊 Detalhes:');
      console.log(`   - ID: ${targetWorkflow.id}`);
      console.log(`   - Nome: ${targetWorkflow.name}`);
      console.log(`   - Ativo: ${targetWorkflow.active ? 'SIM' : 'NÃO'}`);
      console.log(`   - Nós: ${targetWorkflow.nodes ? targetWorkflow.nodes.length : 'N/A'}`);
      console.log('');

      // Obter detalhes completos do workflow
      console.log('🔍 Obtendo detalhes completos...');
      const workflowDetails = await client.getWorkflow(targetWorkflow.id);
      
      console.log('📋 Estrutura atual:');
      if (workflowDetails.nodes) {
        workflowDetails.nodes.forEach((node, index) => {
          console.log(`   ${index + 1}. ${node.name} (${node.type})`);
        });
      }
      console.log('');

      // Verificar se tem webhook
      const hasWebhook = workflowDetails.nodes?.some(node => 
        node.type === 'n8n-nodes-base.webhook' || 
        node.type.includes('webhook') ||
        node.type.includes('trigger')
      );
      
      console.log(`🌐 Tem webhook: ${hasWebhook ? 'SIM' : 'NÃO'}`);
      
    } else {
      console.log('❌ Workflow Unified_User_Pipeline NÃO encontrado!');
      console.log('📋 Workflows disponíveis:');
      workflowList.slice(0, 10).forEach((w, index) => {
        console.log(`   ${index + 1}. ${w.name} (${w.active ? 'ATIVO' : 'INATIVO'})`);
      });
    }

  } catch (error) {
    console.error('❌ Erro ao conectar com n8n API:', error.message);
    
    if (error.message.includes('401')) {
      console.error('🔑 Possível problema: API Key expirada ou inválida');
    } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      console.error('🌐 Possível problema: URL do n8n inacessível');
    }
  }
}

// Executar teste
testN8nConnection().catch(console.error);
