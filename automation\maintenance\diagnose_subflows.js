#!/usr/bin/env node

// Diagnóstico dos Subfluxos - Investigar por que não podem ser ativados
const N8nApiClient = require('./n8n_api_client.js');

class SubflowDiagnostic {
  constructor() {
    this.client = new N8nApiClient(
      'https://n8n-n8n.w9jo16.easypanel.host', 
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
    );
    
    this.essentialSubflows = [
      'Session_Manager_Subflow',
      'Onboarding_Orchestrator_Subflow',
      'Message_Processor_Subflow', 
      'Persistence_Memory_Subflow',
      'Normalize_Extract_Subflow',
      'User_Registry_Access_Subflow'
    ];
  }

  async diagnoseAll() {
    console.log('🔍 DIAGNÓSTICO DOS SUBFLUXOS\n');
    console.log('=' .repeat(50));
    
    try {
      const workflows = await this.client.getWorkflows();
      
      for (const subflowName of this.essentialSubflows) {
        console.log(`\n🔍 Analisando: ${subflowName}`);
        console.log('-' .repeat(40));
        
        const workflow = workflows.data.find(wf => wf.name === subflowName);
        
        if (!workflow) {
          console.log('❌ Workflow não encontrado');
          continue;
        }
        
        await this.diagnoseWorkflow(workflow);
      }
      
      // Verificar credenciais
      console.log('\n🔑 VERIFICANDO CREDENCIAIS');
      console.log('=' .repeat(50));
      await this.checkCredentials();
      
    } catch (error) {
      console.error('❌ Erro no diagnóstico:', error.message);
    }
  }

  async diagnoseWorkflow(workflow) {
    try {
      // Obter detalhes completos do workflow
      const fullWorkflow = await this.client.getWorkflow(workflow.id);
      
      console.log(`ID: ${workflow.id}`);
      console.log(`Status: ${workflow.active ? '🟢 ATIVO' : '🔴 INATIVO'}`);
      console.log(`Nós: ${fullWorkflow.nodes?.length || 0}`);
      console.log(`Conexões: ${Object.keys(fullWorkflow.connections || {}).length}`);
      
      // Analisar nós
      if (fullWorkflow.nodes) {
        const nodeTypes = {};
        const problematicNodes = [];
        
        fullWorkflow.nodes.forEach(node => {
          nodeTypes[node.type] = (nodeTypes[node.type] || 0) + 1;
          
          // Verificar nós problemáticos
          if (this.isProblematicNode(node)) {
            problematicNodes.push({
              name: node.name,
              type: node.type,
              issues: this.getNodeIssues(node)
            });
          }
        });
        
        console.log('Tipos de nós:');
        Object.entries(nodeTypes).forEach(([type, count]) => {
          console.log(`  - ${type}: ${count}`);
        });
        
        if (problematicNodes.length > 0) {
          console.log('⚠️ Nós problemáticos:');
          problematicNodes.forEach(node => {
            console.log(`  - ${node.name} (${node.type})`);
            node.issues.forEach(issue => {
              console.log(`    • ${issue}`);
            });
          });
        } else {
          console.log('✅ Nenhum nó problemático detectado');
        }
      }
      
      // Tentar ativar e capturar erro detalhado
      if (!workflow.active) {
        console.log('\n🧪 Testando ativação...');
        try {
          await this.client.activateWorkflow(workflow.id);
          console.log('✅ Ativação bem-sucedida!');
        } catch (error) {
          console.log(`❌ Erro de ativação: ${error.message}`);
          
          // Tentar obter mais detalhes do erro
          if (error.message.includes('400')) {
            console.log('💡 Possíveis causas do erro 400:');
            console.log('  - Credenciais ausentes ou inválidas');
            console.log('  - Configurações de nó incompletas');
            console.log('  - Dependências não resolvidas');
            console.log('  - Parâmetros obrigatórios não preenchidos');
          }
        }
      }
      
    } catch (error) {
      console.log(`❌ Erro ao analisar workflow: ${error.message}`);
    }
  }

  isProblematicNode(node) {
    // Tipos de nós que frequentemente causam problemas de ativação
    const problematicTypes = [
      'n8n-nodes-base.supabase',
      'n8n-nodes-base.openAi',
      'n8n-nodes-base.httpRequest',
      'n8n-nodes-base.webhook',
      'n8n-nodes-base.executeWorkflow'
    ];
    
    return problematicTypes.includes(node.type);
  }

  getNodeIssues(node) {
    const issues = [];
    
    // Verificar parâmetros obrigatórios
    if (!node.parameters) {
      issues.push('Parâmetros ausentes');
      return issues;
    }
    
    // Verificações específicas por tipo
    switch (node.type) {
      case 'n8n-nodes-base.supabase':
        if (!node.credentials || !node.credentials.supabaseApi) {
          issues.push('Credencial Supabase ausente');
        }
        break;
        
      case 'n8n-nodes-base.openAi':
        if (!node.credentials || !node.credentials.openAiApi) {
          issues.push('Credencial OpenAI ausente');
        }
        break;
        
      case 'n8n-nodes-base.httpRequest':
        if (!node.parameters.url) {
          issues.push('URL não configurada');
        }
        break;
        
      case 'n8n-nodes-base.executeWorkflow':
        if (!node.parameters.workflowId) {
          issues.push('Workflow ID não configurado');
        }
        break;
    }
    
    return issues;
  }

  async checkCredentials() {
    try {
      const credentials = await this.client.getCredentials();
      
      console.log(`Total de credenciais: ${credentials.length}`);
      
      const credentialTypes = {};
      credentials.forEach(cred => {
        credentialTypes[cred.type] = (credentialTypes[cred.type] || 0) + 1;
      });
      
      console.log('Tipos de credenciais disponíveis:');
      Object.entries(credentialTypes).forEach(([type, count]) => {
        console.log(`  - ${type}: ${count}`);
      });
      
      // Verificar credenciais essenciais
      const essentialCredTypes = ['supabaseApi', 'openAiApi', 'httpBasicAuth'];
      
      console.log('\n🔍 Verificando credenciais essenciais:');
      essentialCredTypes.forEach(type => {
        const hasCredential = credentials.some(cred => cred.type === type);
        console.log(`  ${hasCredential ? '✅' : '❌'} ${type}`);
      });
      
    } catch (error) {
      console.log(`❌ Erro ao verificar credenciais: ${error.message}`);
    }
  }

  async generateReport() {
    console.log('\n📋 RELATÓRIO DE DIAGNÓSTICO');
    console.log('=' .repeat(50));
    
    const workflows = await this.client.getWorkflows();
    const subflows = workflows.data.filter(wf => 
      this.essentialSubflows.includes(wf.name)
    );
    
    console.log(`\n📊 Resumo dos Subfluxos:`);
    console.log(`Total encontrados: ${subflows.length}/${this.essentialSubflows.length}`);
    console.log(`Ativos: ${subflows.filter(wf => wf.active).length}`);
    console.log(`Inativos: ${subflows.filter(wf => !wf.active).length}`);
    
    console.log('\n🔍 Status individual:');
    this.essentialSubflows.forEach(name => {
      const workflow = subflows.find(wf => wf.name === name);
      if (workflow) {
        console.log(`  ${workflow.active ? '🟢' : '🔴'} ${name}`);
      } else {
        console.log(`  ❌ ${name} (não encontrado)`);
      }
    });
    
    console.log('\n💡 RECOMENDAÇÕES:');
    console.log('1. Verificar credenciais Supabase e OpenAI');
    console.log('2. Validar configurações de nós HTTP Request');
    console.log('3. Confirmar IDs de workflows referenciados');
    console.log('4. Testar ativação manual via interface n8n');
    console.log('5. Verificar logs do n8n para erros detalhados');
  }
}

// Executar diagnóstico
if (require.main === module) {
  const diagnostic = new SubflowDiagnostic();
  
  diagnostic.diagnoseAll()
    .then(() => diagnostic.generateReport())
    .catch(error => {
      console.error('❌ Erro no diagnóstico:', error);
      process.exit(1);
    });
}

module.exports = SubflowDiagnostic;
