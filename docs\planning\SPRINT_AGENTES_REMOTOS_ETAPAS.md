# 🚀 SPRINT AGENTES REMOTOS - IMPLEMENTAÇÃO EM ETAPAS

**Data**: 19/09/2025  
**Objetivo**: Implementação dos workflows funcionais em etapas controladas  
**Estratégia**: **EVITAR "ONE-SHOT"** - Cada agente trabalha em etapas pequenas e validadas  

---

## 🎯 **LIÇÕES APRENDIDAS**

### **❌ PROBLEMA ANTERIOR: "ONE-SHOT"**
- Agentes tentavam implementar workflow completo de uma vez
- Falhas em conectar todos os nós funcionalmente
- Dificuldade em debugar problemas complexos
- Workflows criados mas não funcionais

### **✅ NOVA ESTRATÉGIA: ETAPAS CONTROLADAS**
- **Etapa 1**: Criar estrutura básica do workflow
- **Etapa 2**: Implementar lógica core (1-2 nós por vez)
- **Etapa 3**: Conectar nós e testar fluxo
- **Etapa 4**: Validar integração com outros workflows
- **Etapa 5**: Testes finais e documentação

---

## 📋 **PLANO DE IMPLEMENTAÇÃO**

### **🔧 FASE PREPARATÓRIA**
**Responsável**: Agente Local (este sprint)
- [x] Limpar arquivos duplicados
- [x] Corrigir informações das 20 tabelas
- [ ] Validar configurações técnicas
- [ ] Preparar documentação base

### **🏗️ FASE 1: INFRAESTRUTURA**
**Agente**: Database_Schema_Generator (Agent 8)
**Duração**: 1 dia
**Etapas**:
1. **Validar schema atual** (20 tabelas)
2. **Criar queries de validação**
3. **Implementar checks de integridade**
4. **Documentar estrutura final**

### **🌐 FASE 2: WORKFLOW PRINCIPAL**
**Agente**: Unified_User_Pipeline (Agent 1)
**Duração**: 2 dias
**Etapas**:
1. **Criar webhook básico** + testar recepção
2. **Implementar extração de CPF** + validar
3. **Adicionar roteamento por access_level** + testar
4. **Conectar com primeiro subfluxo** + validar integração
5. **Conectar todos os 6 subfluxos** + teste completo

### **🔄 FASE 3: SUBFLUXOS (Paralelo)**
**Agentes**: 2, 3, 4, 5, 6, 7
**Duração**: 3 dias (paralelo)

#### **ETAPAS PADRÃO PARA CADA SUBFLUXO:**

**Etapa 1: Estrutura Básica** (30 min)
- Criar workflow com nome correto
- Adicionar Manual Trigger
- Testar ativação básica

**Etapa 2: Lógica Core** (60 min)
- Implementar 1-2 nós principais
- Adicionar código Python básico
- Testar execução individual

**Etapa 3: Conexões** (45 min)
- Conectar nós criados
- Testar fluxo de dados
- Validar saídas

**Etapa 4: Integração** (30 min)
- Testar chamada via Execute Workflow
- Validar dados de entrada/saída
- Confirmar funcionamento

**Etapa 5: Validação Final** (15 min)
- Teste completo do subfluxo
- Documentar funcionamento
- Marcar como concluído

---

## 🎯 **OBJETIVOS ESPECÍFICOS POR AGENTE**

### **Agent 1 - Unified_User_Pipeline**
- [ ] **Etapa 1**: Webhook recebendo dados do WhatsApp
- [ ] **Etapa 2**: Extração de CPF funcionando
- [ ] **Etapa 3**: Roteamento por access_level (1-7)
- [ ] **Etapa 4**: Execução do primeiro subfluxo
- [ ] **Etapa 5**: Execução de todos os 6 subfluxos

### **Agent 2 - Normalize_Extract_Subflow**
- [ ] **Etapa 1**: Recepção de dados via Execute Workflow
- [ ] **Etapa 2**: Normalização de CPF e phone
- [ ] **Etapa 3**: Limpeza de mensagem
- [ ] **Etapa 4**: Estruturação de dados de saída
- [ ] **Etapa 5**: Validação completa

### **Agent 3 - User_Registry_Access_Subflow**
- [ ] **Etapa 1**: Query Postgres para buscar usuário
- [ ] **Etapa 2**: Lógica IF (usuário existe?)
- [ ] **Etapa 3**: Criação de novo usuário (Supabase)
- [ ] **Etapa 4**: Atualização de last_access
- [ ] **Etapa 5**: Determinação de access_level

### **Agent 4 - Session_Manager_Subflow**
- [ ] **Etapa 1**: Busca de sessão ativa
- [ ] **Etapa 2**: Criação de nova sessão
- [ ] **Etapa 3**: Inserção de mensagem
- [ ] **Etapa 4**: Atualização de metadados
- [ ] **Etapa 5**: Controle de timeout

### **Agent 5 - Onboarding_Orchestrator_Subflow**
- [ ] **Etapa 1**: Análise de progresso atual
- [ ] **Etapa 2**: Switch por access_level
- [ ] **Etapa 3**: Lógica específica por nível
- [ ] **Etapa 4**: Atualização de progresso
- [ ] **Etapa 5**: Gamificação e badges

### **Agent 6 - Message_Processor_Subflow**
- [ ] **Etapa 1**: Categorização de mensagem
- [ ] **Etapa 2**: Chamada OpenAI API
- [ ] **Etapa 3**: Processamento de resposta
- [ ] **Etapa 4**: Envio via Evolution API
- [ ] **Etapa 5**: Log de interação

### **Agent 7 - Persistence_Memory_Subflow**
- [ ] **Etapa 1**: Recepção de dados consolidados
- [ ] **Etapa 2**: Persistência em chat_memory_history
- [ ] **Etapa 3**: Atualização de contexto
- [ ] **Etapa 4**: Consolidação de sessão
- [ ] **Etapa 5**: Relatórios de atividade

---

## ✅ **CRITÉRIOS DE SUCESSO POR ETAPA**

### **VALIDAÇÃO OBRIGATÓRIA ENTRE ETAPAS**
1. **Teste individual** da funcionalidade implementada
2. **Validação de dados** de entrada e saída
3. **Confirmação de conexões** entre nós
4. **Teste de integração** com workflows relacionados
5. **Documentação** do que foi implementado

### **CHECKPOINTS DE QUALIDADE**
- ✅ Nós conectados funcionalmente
- ✅ Código Python (não JavaScript)
- ✅ Dados preservados entre nós
- ✅ Tempo de resposta < 3 segundos
- ✅ Tratamento de erros implementado

---

## 🚨 **REGRAS CRÍTICAS**

### **PARA TODOS OS AGENTES**
1. **NUNCA pular etapas** - Cada etapa deve ser validada
2. **SEMPRE testar** antes de passar para próxima etapa
3. **SEMPRE usar Python** nos nós Code
4. **SEMPRE conectar nós** funcionalmente
5. **SEMPRE preservar dados** originais

### **COMUNICAÇÃO ENTRE ETAPAS**
- Documentar o que foi feito em cada etapa
- Reportar problemas imediatamente
- Validar integração antes de finalizar
- Confirmar funcionamento completo

---

**Status**: Pronto para execução pelos agentes remotos em etapas controladas
