name: Validate Workflows

on:
  # Execução em todos os pushes
  push:
    branches: [ main, develop ]
    paths:
      - 'workflows/**'
      - 'scripts/**'
      - 'package.json'

  # Execução em pull requests
  pull_request:
    branches: [ main ]
    paths:
      - 'workflows/**'
      - 'scripts/**'
      - 'package.json'

  # Execução manual
  workflow_dispatch:

  # Execução diária para verificação de integridade
  schedule:
    - cron: '0 8 * * *'  # 8:00 AM UTC daily

jobs:
  validate-structure:
    name: Validate Project Structure
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Validate directory structure
        run: |
          echo "🔍 Validating project structure..."
          
          # Check required directories
          required_dirs=(
            "workflows/active"
            "workflows/archive"
            "scripts"
            "docs"
            ".github/workflows"
          )
          
          for dir in "${required_dirs[@]}"; do
            if [ ! -d "$dir" ]; then
              echo "❌ Missing required directory: $dir"
              exit 1
            else
              echo "✅ Found directory: $dir"
            fi
          done
          
          # Check required files
          required_files=(
            "package.json"
            ".gitignore"
            "README.md"
          )
          
          for file in "${required_files[@]}"; do
            if [ ! -f "$file" ]; then
              echo "❌ Missing required file: $file"
              exit 1
            else
              echo "✅ Found file: $file"
            fi
          done
          
          echo "✅ Project structure validation passed!"

  validate-workflows:
    name: Validate Workflow Files
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Validate JSON syntax
        run: |
          echo "🔍 Validating JSON syntax..."
          
          json_valid=true
          
          for file in workflows/active/*.json; do
            if [ -f "$file" ]; then
              echo "Checking $file..."
              if ! node -e "JSON.parse(require('fs').readFileSync('$file', 'utf8'))"; then
                echo "❌ Invalid JSON: $file"
                json_valid=false
              else
                echo "✅ Valid JSON: $file"
              fi
            fi
          done
          
          if [ "$json_valid" = false ]; then
            echo "❌ JSON validation failed!"
            exit 1
          fi
          
          echo "✅ All JSON files are valid!"
          
      - name: Validate workflow structure
        run: |
          echo "🔍 Validating workflow structure..."
          npm run workflows:validate
          
      - name: Check for required workflows
        run: |
          echo "🔍 Checking for required workflows..."
          
          required_workflows=(
            "WhatsApp_Message_Router_v1.json"
            "SuperUser_Authentication_v1.json"
            "Session_Manager_v1.json"
            "Main_Orchestrator_Agent_v2.json"
            "Message_Processor_v1.json"
            "Database_Schema_Generator_v1.json"
          )
          
          missing_workflows=()
          
          for workflow in "${required_workflows[@]}"; do
            if [ ! -f "workflows/active/$workflow" ]; then
              missing_workflows+=("$workflow")
              echo "❌ Missing required workflow: $workflow"
            else
              echo "✅ Found required workflow: $workflow"
            fi
          done
          
          if [ ${#missing_workflows[@]} -gt 0 ]; then
            echo "❌ Missing ${#missing_workflows[@]} required workflows!"
            echo "Run 'npm run workflows:organize' to organize workflows"
            exit 1
          fi
          
          echo "✅ All required workflows found!"

  validate-scripts:
    name: Validate Scripts
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Validate script syntax
        run: |
          echo "🔍 Validating script syntax..."
          
          # Validate JavaScript files
          for file in scripts/*.js; do
            if [ -f "$file" ]; then
              echo "Checking $file..."
              if ! node --check "$file"; then
                echo "❌ Invalid JavaScript: $file"
                exit 1
              else
                echo "✅ Valid JavaScript: $file"
              fi
            fi
          done
          
          # Validate shell scripts
          for file in scripts/*.sh; do
            if [ -f "$file" ]; then
              echo "Checking $file..."
              if ! bash -n "$file"; then
                echo "❌ Invalid shell script: $file"
                exit 1
              else
                echo "✅ Valid shell script: $file"
              fi
            fi
          done
          
          echo "✅ All scripts have valid syntax!"
          
      - name: Test script execution (dry run)
        run: |
          echo "🔍 Testing script execution..."
          
          # Test organize script
          if [ -f "scripts/organize_workflows.js" ]; then
            echo "Testing organize_workflows.js..."
            node -e "
              const organizer = require('./scripts/organize_workflows.js');
              console.log('✅ organize_workflows.js can be imported');
            "
          fi
          
          # Test sync scripts
          for script in sync_from_n8n.js sync_to_n8n.js sync_bidirectional.js; do
            if [ -f "scripts/$script" ]; then
              echo "Testing $script..."
              node -e "
                const sync = require('./scripts/$script');
                console.log('✅ $script can be imported');
              "
            fi
          done

  security-check:
    name: Security Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Check for sensitive data
        run: |
          echo "🔍 Checking for sensitive data..."
          
          # Check for potential secrets in workflow files
          if grep -r -i "password\|secret\|key\|token" workflows/active/ --include="*.json" | grep -v "N8N_API_KEY\|N8N_API_URL"; then
            echo "❌ Potential sensitive data found in workflow files!"
            echo "Please review and remove any hardcoded secrets."
            exit 1
          fi
          
          # Check for API keys in scripts
          if grep -r -i "api.*key.*=" scripts/ --include="*.js" | grep -v "process.env\|CONFIG.N8N_API_KEY"; then
            echo "❌ Potential hardcoded API keys found in scripts!"
            echo "Please use environment variables for API keys."
            exit 1
          fi
          
          echo "✅ No sensitive data found!"

  generate-report:
    name: Generate Validation Report
    runs-on: ubuntu-latest
    needs: [validate-structure, validate-workflows, validate-scripts, security-check]
    if: always()
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Generate validation report
        run: |
          echo "📊 Generating validation report..."
          
          cat > validation-report.md << EOF
          # Workflow Validation Report
          
          **Date:** $(date -u +%Y-%m-%dT%H:%M:%SZ)
          **Commit:** ${{ github.sha }}
          **Branch:** ${{ github.ref_name }}
          
          ## Validation Results
          
          - **Structure Validation:** ${{ needs.validate-structure.result }}
          - **Workflow Validation:** ${{ needs.validate-workflows.result }}
          - **Script Validation:** ${{ needs.validate-scripts.result }}
          - **Security Check:** ${{ needs.security-check.result }}
          
          ## Workflow Files
          $(find workflows/active -name "*.json" | wc -l) workflow files found:
          $(ls -la workflows/active/*.json 2>/dev/null | awk '{print "- " $9 " (" $5 " bytes)"}' || echo "No workflow files found")
          
          ## Next Steps
          $(if [ "${{ needs.validate-structure.result }}" = "success" ] && [ "${{ needs.validate-workflows.result }}" = "success" ] && [ "${{ needs.validate-scripts.result }}" = "success" ] && [ "${{ needs.security-check.result }}" = "success" ]; then
            echo "✅ All validations passed! Ready for deployment."
          else
            echo "❌ Some validations failed. Please review and fix issues before proceeding."
          fi)
          EOF
          
          cat validation-report.md
          
      - name: Upload validation report
        uses: actions/upload-artifact@v4
        with:
          name: validation-report
          path: validation-report.md
          retention-days: 30
