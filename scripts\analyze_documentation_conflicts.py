#!/usr/bin/env python3
"""
Documentation Conflicts Analysis Script
Analisa conflitos entre documentação e estado real do sistema AI Comtxae
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime

class DocumentationAnalyzer:
    def __init__(self, workspace_path="/mnt/persist/workspace"):
        self.workspace_path = Path(workspace_path)
        self.conflicts = {
            'schema_references': [],
            'workflow_names': [],
            'architecture_diagrams': [],
            'api_endpoints': [],
            'database_queries': [],
            'table_counts': [],
            'user_model_references': []
        }
        
        # Estado real do sistema (baseado na auditoria)
        self.real_state = {
            'total_tables': 20,
            'user_model': 'CPF unificado',
            'primary_key': 'cpf (character varying)',
            'workflows': [
                'Unified_User_Pipeline',
                'Normalize_Extract_Subflow', 
                'User_Registry_Access_Subflow',
                'Session_Manager_Subflow',
                'Onboarding_Orchestrator_Subflow',
                'Message_Processor_Subflow',
                'Persistence_Memory_Subflow'
            ],
            'supabase_project': 'assistente_v0.1',
            'n8n_version': '1.111.0'
        }

    def analyze_documentation_conflicts(self):
        """Análise completa de conflitos na documentação"""
        print("🔍 Iniciando análise de conflitos na documentação...")
        
        # Encontrar todos os arquivos markdown
        md_files = list(self.workspace_path.rglob("*.md"))
        print(f"📄 Encontrados {len(md_files)} arquivos markdown")
        
        for md_file in md_files:
            try:
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self._analyze_file_conflicts(md_file, content)
            except Exception as e:
                print(f"❌ Erro ao analisar {md_file}: {e}")
        
        return self.conflicts

    def _analyze_file_conflicts(self, file_path, content):
        """Analisa conflitos em um arquivo específico"""
        relative_path = file_path.relative_to(self.workspace_path)
        
        # 1. Verificar referências ao modelo antigo superuser/non-superuser
        if re.search(r'super_?user|non.?super.?user', content, re.IGNORECASE):
            self.conflicts['user_model_references'].append({
                'file': str(relative_path),
                'issue': 'Referência ao modelo superuser/non-superuser obsoleto',
                'expected': 'CPF unificado'
            })
        
        # 2. Verificar contagem de tabelas incorreta
        table_matches = re.findall(r'(\d+)\s*tabelas?', content, re.IGNORECASE)
        for match in table_matches:
            count = int(match)
            if count != self.real_state['total_tables'] and count > 5:  # Ignorar números muito baixos
                self.conflicts['table_counts'].append({
                    'file': str(relative_path),
                    'documented': count,
                    'real': self.real_state['total_tables'],
                    'issue': f'Contagem incorreta: {count} vs {self.real_state["total_tables"]}'
                })
        
        # 3. Verificar referências a workflows obsoletos
        old_workflows = [
            'SuperUser Authentication',
            'Session Manager v1',
            'Main Orchestrator Agent',
            'Message Processor v1'
        ]
        
        for old_workflow in old_workflows:
            if old_workflow in content:
                self.conflicts['workflow_names'].append({
                    'file': str(relative_path),
                    'old_name': old_workflow,
                    'issue': 'Workflow name obsoleto',
                    'expected': 'Unified_User_Pipeline architecture'
                })
        
        # 4. Verificar queries SQL com estrutura antiga
        if re.search(r'super_users\.id|user_roles\.user_id', content):
            self.conflicts['database_queries'].append({
                'file': str(relative_path),
                'issue': 'Query SQL usando estrutura obsoleta',
                'expected': 'users.cpf como PK'
            })
        
        # 5. Verificar endpoints de API obsoletos
        if re.search(r'/api/super.?users|/users/\{id\}', content):
            self.conflicts['api_endpoints'].append({
                'file': str(relative_path),
                'issue': 'Endpoint de API obsoleto',
                'expected': '/users/{cpf} endpoints'
            })

    def generate_conflict_report(self):
        """Gera relatório detalhado de conflitos"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_conflicts': sum(len(conflicts) for conflicts in self.conflicts.values()),
            'conflicts_by_type': {k: len(v) for k, v in self.conflicts.items()},
            'detailed_conflicts': self.conflicts,
            'recommendations': self._generate_recommendations()
        }
        
        return report

    def _generate_recommendations(self):
        """Gera recomendações para correção"""
        recommendations = []
        
        if self.conflicts['user_model_references']:
            recommendations.append({
                'priority': 'HIGH',
                'action': 'Substituir todas as referências superuser/non-superuser por CPF unificado',
                'files_affected': len(self.conflicts['user_model_references'])
            })
        
        if self.conflicts['table_counts']:
            recommendations.append({
                'priority': 'HIGH', 
                'action': f'Atualizar contagem de tabelas para {self.real_state["total_tables"]}',
                'files_affected': len(self.conflicts['table_counts'])
            })
        
        if self.conflicts['workflow_names']:
            recommendations.append({
                'priority': 'MEDIUM',
                'action': 'Atualizar nomes de workflows para arquitetura Unified_User_Pipeline',
                'files_affected': len(self.conflicts['workflow_names'])
            })
        
        return recommendations

    def save_report(self, output_path="docs/architecture/CONFLICTS_ANALYSIS_REPORT.json"):
        """Salva relatório de conflitos"""
        report = self.generate_conflict_report()
        
        output_file = self.workspace_path / output_path
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📊 Relatório salvo em: {output_file}")
        return report

def main():
    """Função principal"""
    print("🤖 Documentation Conflicts Analyzer - AI Comtxae")
    print("=" * 50)
    
    analyzer = DocumentationAnalyzer()
    
    # Executar análise
    conflicts = analyzer.analyze_documentation_conflicts()
    
    # Gerar e salvar relatório
    report = analyzer.save_report()
    
    # Resumo dos resultados
    print(f"\n📋 RESUMO DA ANÁLISE:")
    print(f"Total de conflitos encontrados: {report['total_conflicts']}")
    print(f"Arquivos com referências obsoletas: {len(conflicts['user_model_references'])}")
    print(f"Contagens incorretas de tabelas: {len(conflicts['table_counts'])}")
    print(f"Workflows obsoletos: {len(conflicts['workflow_names'])}")
    
    if report['total_conflicts'] > 0:
        print(f"\n⚠️  AÇÃO NECESSÁRIA: {len(report['recommendations'])} recomendações geradas")
        for rec in report['recommendations']:
            print(f"  • {rec['priority']}: {rec['action']}")
    else:
        print("\n✅ Nenhum conflito encontrado - documentação alinhada!")
    
    return report

if __name__ == "__main__":
    main()
