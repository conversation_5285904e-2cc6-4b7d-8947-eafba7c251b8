// Enhanced n8n API Client for Workflow Management
class N8nApiClient {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.headers = {
      'X-N8N-API-KEY': apiKey,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  // Create a new workflow
  async createWorkflow(workflowData) {
    try {
      // Only include essential properties for n8n API
      // Based on testing, n8n API requires: name, nodes, connections, settings
      const apiWorkflowData = {
        name: workflowData.name,
        nodes: workflowData.nodes || [],
        connections: workflowData.connections || {},
        settings: workflowData.settings || { executionOrder: 'v1' }
      };

      // Note: All other properties cause "additional properties" errors:
      // - staticData (n8n handles this automatically)
      // - meta (n8n handles this automatically)
      // - pinData (n8n handles this automatically)
      // - tags (n8n handles this automatically, and tags with timestamps cause errors)
      // - active (managed separately via activation API)
      // - triggerCount (auto-generated by n8n)
      // - updatedAt (auto-generated by n8n)
      // - createdAt (auto-generated by n8n)
      // - versionId (auto-generated by n8n)
      // - id (auto-generated by n8n)

      // Debug: console.log(`📤 Sending workflow data for "${workflowData.name}":`, JSON.stringify(apiWorkflowData, null, 2).substring(0, 1000) + '...');

      const response = await fetch(`${this.baseUrl}/api/v1/workflows`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(apiWorkflowData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API Error Details:`, errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating workflow:', error);
      throw error;
    }
  }

  // Update an existing workflow
  async updateWorkflow(workflowId, workflowData) {
    try {
      // Only include essential properties for n8n API - be very strict
      const apiWorkflowData = {
        name: workflowData.name,
        nodes: workflowData.nodes || [],
        connections: workflowData.connections || {}
      };

      // Only add settings if it exists and is not empty
      if (workflowData.settings && Object.keys(workflowData.settings).length > 0) {
        apiWorkflowData.settings = workflowData.settings;
      }

      // Debug: Log what we're sending (first 500 chars)
      const debugData = JSON.stringify(apiWorkflowData, null, 2);
      console.log(`📤 Updating workflow "${workflowData.name}" with data:`, debugData.substring(0, 500) + '...');

      const response = await fetch(`${this.baseUrl}/api/v1/workflows/${workflowId}`, {
        method: 'PUT',
        headers: this.headers,
        body: JSON.stringify(apiWorkflowData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API Error Details:`, errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating workflow:', error);
      throw error;
    }
  }

  // Activate a workflow
  async activateWorkflow(workflowId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/workflows/${workflowId}/activate`, {
        method: 'POST',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error activating workflow:', error);
      throw error;
    }
  }

  // Deactivate a workflow
  async deactivateWorkflow(workflowId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/workflows/${workflowId}/deactivate`, {
        method: 'POST',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deactivating workflow:', error);
      throw error;
    }
  }

  // Get all workflows
  async getWorkflows() {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/workflows`, {
        method: 'GET',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting workflows:', error);
      throw error;
    }
  }

  // Get a specific workflow by ID
  async getWorkflow(workflowId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/workflows/${workflowId}`, {
        method: 'GET',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting workflow:', error);
      throw error;
    }
  }

  // Delete a workflow
  async deleteWorkflow(workflowId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/workflows/${workflowId}`, {
        method: 'DELETE',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting workflow:', error);
      throw error;
    }
  }

  // Find workflow by name
  async findWorkflowByName(workflowName) {
    try {
      const workflows = await this.getWorkflows();
      return workflows.data?.find(workflow => workflow.name === workflowName) || null;
    } catch (error) {
      console.error('Error finding workflow by name:', error);
      throw error;
    }
  }

  // Deploy workflow (create or update if exists)
  async deployWorkflow(workflowData) {
    try {
      const existingWorkflow = await this.findWorkflowByName(workflowData.name);

      if (existingWorkflow) {
        console.log(`Updating existing workflow: ${workflowData.name}`);
        return await this.updateWorkflow(existingWorkflow.id, workflowData);
      } else {
        console.log(`Creating new workflow: ${workflowData.name}`);
        return await this.createWorkflow(workflowData);
      }
    } catch (error) {
      console.error('Error deploying workflow:', error);
      throw error;
    }
  }

  // Bulk deploy multiple workflows
  async bulkDeployWorkflows(workflowsData, options = {}) {
    const {
      activateAfterDeploy = true,
      continueOnError = true,
      delayBetweenDeployments = 1000
    } = options;

    const results = [];

    console.log(`Starting bulk deployment of ${workflowsData.length} workflows...`);

    for (let i = 0; i < workflowsData.length; i++) {
      const workflowData = workflowsData[i];

      try {
        console.log(`[${i + 1}/${workflowsData.length}] Deploying: ${workflowData.name}`);

        // Deploy workflow
        const deployResult = await this.deployWorkflow(workflowData);

        // Activate if requested
        if (activateAfterDeploy && deployResult.id) {
          try {
            await this.activateWorkflow(deployResult.id);
            console.log(`✅ Activated: ${workflowData.name}`);
          } catch (activationError) {
            console.warn(`⚠️ Failed to activate ${workflowData.name}:`, activationError.message);
          }
        }

        results.push({
          name: workflowData.name,
          status: 'success',
          id: deployResult.id,
          activated: activateAfterDeploy,
          result: deployResult
        });

        console.log(`✅ Successfully deployed: ${workflowData.name}`);

      } catch (error) {
        console.error(`❌ Failed to deploy ${workflowData.name}:`, error.message);

        results.push({
          name: workflowData.name,
          status: 'error',
          error: error.message,
          activated: false
        });

        if (!continueOnError) {
          throw new Error(`Bulk deployment stopped at ${workflowData.name}: ${error.message}`);
        }
      }

      // Add delay between deployments to avoid rate limiting
      if (i < workflowsData.length - 1 && delayBetweenDeployments > 0) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenDeployments));
      }
    }

    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;

    console.log(`\n📊 Bulk deployment completed:`);
    console.log(`✅ Successful: ${successCount}`);
    console.log(`❌ Failed: ${errorCount}`);
    console.log(`📋 Total: ${results.length}`);

    return {
      summary: {
        total: results.length,
        successful: successCount,
        failed: errorCount,
        success_rate: ((successCount / results.length) * 100).toFixed(1) + '%'
      },
      results: results
    };
  }

  // Load workflows from file system
  async loadWorkflowsFromDirectory(directoryPath) {
    const fs = require('fs').promises;
    const path = require('path');

    try {
      const files = await fs.readdir(directoryPath);
      const jsonFiles = files.filter(file => file.endsWith('.json'));

      const workflows = [];

      for (const file of jsonFiles) {
        try {
          const filePath = path.join(directoryPath, file);
          const fileContent = await fs.readFile(filePath, 'utf8');
          const workflowData = JSON.parse(fileContent);

          workflows.push({
            filename: file,
            ...workflowData
          });

        } catch (error) {
          console.warn(`⚠️ Failed to load workflow from ${file}:`, error.message);
        }
      }

      console.log(`📁 Loaded ${workflows.length} workflows from ${directoryPath}`);
      return workflows;

    } catch (error) {
      console.error('Error loading workflows from directory:', error);
      throw error;
    }
  }

  // Deploy entire ecosystem
  async deployEcosystem(productFlowsPath, infrastructureFlowsPath, options = {}) {
    try {
      console.log('🚀 Starting ecosystem deployment...\n');

      // Load all workflows
      const productFlows = await this.loadWorkflowsFromDirectory(productFlowsPath);
      const infrastructureFlows = await this.loadWorkflowsFromDirectory(infrastructureFlowsPath);

      // Deploy infrastructure flows first (dependencies)
      console.log('📋 Phase 1: Deploying Infrastructure Flows...');
      const infraResults = await this.bulkDeployWorkflows(infrastructureFlows, options);

      // Deploy product flows
      console.log('\n📋 Phase 2: Deploying Product Flows...');
      const productResults = await this.bulkDeployWorkflows(productFlows, options);

      // Combined results
      const combinedResults = {
        summary: {
          total: infraResults.summary.total + productResults.summary.total,
          successful: infraResults.summary.successful + productResults.summary.successful,
          failed: infraResults.summary.failed + productResults.summary.failed,
          infrastructure_flows: infraResults.summary,
          product_flows: productResults.summary
        },
        infrastructure_results: infraResults.results,
        product_results: productResults.results
      };

      combinedResults.summary.success_rate =
        ((combinedResults.summary.successful / combinedResults.summary.total) * 100).toFixed(1) + '%';

      console.log('\n🎉 Ecosystem deployment completed!');
      console.log(`📊 Overall Success Rate: ${combinedResults.summary.success_rate}`);

      return combinedResults;

    } catch (error) {
      console.error('❌ Ecosystem deployment failed:', error);
      throw error;
    }
  }

  // Credential Management Methods

  // Get all credentials
  async getCredentials() {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/credentials`, {
        method: 'GET',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data || result;
    } catch (error) {
      console.error('Error getting credentials:', error);
      throw error;
    }
  }

  // Get specific credential
  async getCredential(credentialId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/credentials/${credentialId}`, {
        method: 'GET',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data || result;
    } catch (error) {
      console.error('Error getting credential:', error);
      throw error;
    }
  }

  // Create new credential
  async createCredential(credentialData) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/credentials`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(credentialData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      return result.data || result;
    } catch (error) {
      console.error('Error creating credential:', error);
      throw error;
    }
  }

  // Update existing credential
  async updateCredential(credentialId, credentialData) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/credentials/${credentialId}`, {
        method: 'PATCH',
        headers: this.headers,
        body: JSON.stringify(credentialData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      return result.data || result;
    } catch (error) {
      console.error('Error updating credential:', error);
      throw error;
    }
  }

  // Delete credential
  async deleteCredential(credentialId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/credentials/${credentialId}`, {
        method: 'DELETE',
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data || result;
    } catch (error) {
      console.error('Error deleting credential:', error);
      throw error;
    }
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = N8nApiClient;
}