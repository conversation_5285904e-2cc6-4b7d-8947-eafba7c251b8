#!/usr/bin/env node

/**
 * Architecture Validator Agent (Agent 7)
 * 
 * Valida se a arquitetura do sistema está em conformidade com o padrão Unified_User_Pipeline
 * Verifica violações arquiteturais e gera relatório de conformidade
 * 
 * Usage: node automation/maintenance/architecture_validator.js
 */

require('dotenv').config();
const N8nApiClient = require('./n8n_api_client.js');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host/',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

// Expected architecture according to Unified_User_Pipeline standard
const EXPECTED_ARCHITECTURE = {
  mainWorkflow: 'Unified_User_Pipeline',
  expectedSubflows: [
    'Normalize_Extract_Subflow',
    'User_Registry_Access_Subflow', 
    'Session_Manager_Subflow',
    'Onboarding_Orchestrator_Subflow',
    'Message_Processor_Subflow',
    'Persistence_Memory_Subflow'
  ],
  webhookRule: 'ONLY_ONE_WEBHOOK_IN_MAIN_WORKFLOW'
};

class ArchitectureValidator {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.validationResults = {
      timestamp: new Date().toISOString(),
      totalWorkflows: 0,
      activeWorkflows: 0,
      inactiveWorkflows: 0,
      webhookWorkflows: [],
      unifiedPipelineFound: false,
      subflowsFound: [],
      violations: [],
      recommendations: [],
      conformityScore: 0
    };
  }

  // Main validation process
  async validateArchitecture() {
    try {
      console.log('🏗️ ARCHITECTURE VALIDATOR AGENT 7 - Iniciando validação...\n');
      console.log('🎯 Padrão arquitetural: Unified_User_Pipeline');
      console.log('🔍 Instância n8n:', CONFIG.n8nApiUrl);
      console.log('');

      // Get all workflows
      const workflows = await this.client.getWorkflows();
      this.validationResults.totalWorkflows = workflows.data.length;
      this.validationResults.activeWorkflows = workflows.data.filter(w => w.active).length;
      this.validationResults.inactiveWorkflows = workflows.data.filter(w => !w.active).length;

      console.log(`📊 WORKFLOWS ENCONTRADOS: ${workflows.data.length}`);
      console.log(`   ✅ Ativos: ${this.validationResults.activeWorkflows}`);
      console.log(`   ⚠️ Inativos: ${this.validationResults.inactiveWorkflows}`);
      console.log('');

      // Analyze each workflow
      await this.analyzeWorkflows(workflows.data);

      // Validate architectural compliance
      this.validateCompliance();

      // Calculate conformity score
      this.calculateConformityScore();

      // Generate recommendations
      this.generateRecommendations();

      // Display results
      this.displayResults();

      // Save validation report
      await this.saveValidationReport();

      return this.validationResults;

    } catch (error) {
      console.error('❌ Erro na validação arquitetural:', error.message);
      throw error;
    }
  }

  // Analyze individual workflows
  async analyzeWorkflows(workflows) {
    console.log('🔍 ANALISANDO WORKFLOWS INDIVIDUAIS:\n');

    for (const workflow of workflows) {
      console.log(`📋 ${workflow.name} - ${workflow.active ? '✅ ATIVO' : '⚠️ INATIVO'}`);

      // Check if it's the main workflow
      if (workflow.name === EXPECTED_ARCHITECTURE.mainWorkflow) {
        this.validationResults.unifiedPipelineFound = true;
        console.log('   🎯 WORKFLOW PRINCIPAL IDENTIFICADO');
      }

      // Check if it's a subflow
      if (workflow.name.includes('_Subflow')) {
        this.validationResults.subflowsFound.push(workflow.name);
        console.log('   🔗 SUBFLUXO IDENTIFICADO');
      }

      // Get workflow details to check for webhooks and node types
      try {
        const details = await this.client.getWorkflow(workflow.id);
        await this.analyzeWorkflowDetails(workflow, details);
      } catch (error) {
        console.log(`   ❌ Erro ao buscar detalhes: ${error.message}`);
      }

      console.log('');
    }
  }

  // Analyze workflow details (nodes, connections, etc.)
  async analyzeWorkflowDetails(workflow, details) {
    // Check for webhook nodes
    const webhookNodes = details.nodes.filter(node => 
      node.type === 'n8n-nodes-base.webhook' || 
      node.type === 'n8n-nodes-base.webhookTrigger'
    );

    if (webhookNodes.length > 0) {
      this.validationResults.webhookWorkflows.push({
        name: workflow.name,
        active: workflow.active,
        webhookCount: webhookNodes.length
      });
      console.log(`   🌐 WEBHOOK DETECTADO (${webhookNodes.length} nodes)`);
    }

    // Check for Execute Workflow nodes (should be used in subflows)
    const executeWorkflowNodes = details.nodes.filter(node => 
      node.type === 'n8n-nodes-base.executeWorkflow'
    );

    if (executeWorkflowNodes.length > 0) {
      console.log(`   🔄 ${executeWorkflowNodes.length} Execute Workflow nodes encontrados`);
    }

    // Check for deprecated or incorrect node types
    const deprecatedNodes = details.nodes.filter(node => 
      node.type.includes('old_') || 
      node.type.includes('deprecated_') ||
      node.type.includes('temp_')
    );

    if (deprecatedNodes.length > 0) {
      console.log(`   ⚠️ ${deprecatedNodes.length} nós obsoletos encontrados`);
    }
  }

  // Validate architectural compliance
  validateCompliance() {
    console.log('🔍 ANÁLISE DE CONFORMIDADE ARQUITETURAL:\n');

    // Rule 1: Only one webhook in the entire system
    this.validateWebhookRule();

    // Rule 2: Main workflow must exist
    this.validateMainWorkflow();

    // Rule 3: All expected subflows must exist
    this.validateSubflows();

    // Rule 4: Hierarchical structure validation
    this.validateHierarchy();
  }

  // Validate webhook rule (only one webhook in main workflow)
  validateWebhookRule() {
    // Filter only ACTIVE workflows with webhooks (inactive ones don't matter)
    const activeWebhookWorkflows = this.validationResults.webhookWorkflows.filter(workflow => {
      // Find the workflow in our analysis to check if it's active
      return workflow.active === true;
    });

    if (activeWebhookWorkflows.length === 0) {
      this.validationResults.violations.push({
        severity: 'CRÍTICA',
        type: 'WEBHOOK_MISSING',
        description: 'Nenhum webhook ativo encontrado no sistema',
        impact: 'Sistema não pode receber mensagens do WhatsApp'
      });
    } else if (activeWebhookWorkflows.length === 1) {
      if (activeWebhookWorkflows[0].name === EXPECTED_ARCHITECTURE.mainWorkflow) {
        console.log('✅ WEBHOOK ÚNICO: Apenas Unified_User_Pipeline tem webhook ativo (CONFORME)');
      } else {
        this.validationResults.violations.push({
          severity: 'CRÍTICA',
          type: 'WEBHOOK_WRONG_LOCATION',
          description: `Webhook ativo encontrado em '${activeWebhookWorkflows[0].name}' ao invés de '${EXPECTED_ARCHITECTURE.mainWorkflow}'`,
          impact: 'Violação do padrão arquitetural de entrada única'
        });
      }
    } else {
      this.validationResults.violations.push({
        severity: 'CRÍTICA',
        type: 'MULTIPLE_ACTIVE_WEBHOOKS',
        description: `Múltiplos webhooks ativos encontrados: ${activeWebhookWorkflows.map(w => w.name).join(', ')}`,
        impact: 'Violação grave do padrão de entrada única'
      });
    }

    // Report inactive webhooks as informational (not violations)
    const inactiveWebhookWorkflows = this.validationResults.webhookWorkflows.filter(workflow => {
      return workflow.active === false;
    });

    if (inactiveWebhookWorkflows.length > 0) {
      console.log(`ℹ️ WEBHOOKS INATIVOS: ${inactiveWebhookWorkflows.length} workflows com webhooks inativos (não violam arquitetura)`);
    }
  }

  // Validate main workflow existence
  validateMainWorkflow() {
    if (!this.validationResults.unifiedPipelineFound) {
      this.validationResults.violations.push({
        severity: 'CRÍTICA',
        type: 'MAIN_WORKFLOW_MISSING',
        description: `Workflow principal '${EXPECTED_ARCHITECTURE.mainWorkflow}' não encontrado`,
        impact: 'Sistema não tem ponto de entrada principal'
      });
    } else {
      console.log('✅ WORKFLOW PRINCIPAL: Unified_User_Pipeline encontrado (CONFORME)');
    }
  }

  // Validate subflows
  validateSubflows() {
    const missingSubflows = EXPECTED_ARCHITECTURE.expectedSubflows.filter(expected => 
      !this.validationResults.subflowsFound.includes(expected)
    );

    if (missingSubflows.length === 0) {
      console.log('✅ SUBFLUXOS: Todos os 6 subfluxos encontrados (CONFORME)');
    } else {
      this.validationResults.violations.push({
        severity: 'ALTA',
        type: 'MISSING_SUBFLOWS',
        description: `Subfluxos faltando: ${missingSubflows.join(', ')}`,
        impact: 'Funcionalidades essenciais não implementadas'
      });
    }

    // Check for extra subflows not in specification
    const extraSubflows = this.validationResults.subflowsFound.filter(found => 
      !EXPECTED_ARCHITECTURE.expectedSubflows.includes(found)
    );

    if (extraSubflows.length > 0) {
      this.validationResults.violations.push({
        severity: 'MÉDIA',
        type: 'EXTRA_SUBFLOWS',
        description: `Subfluxos não especificados: ${extraSubflows.join(', ')}`,
        impact: 'Possível desvio da arquitetura padrão'
      });
    }
  }

  // Validate hierarchical structure
  validateHierarchy() {
    // This would require deeper analysis of Execute Workflow nodes
    // For now, we'll add a placeholder for future implementation
    console.log('🔗 HIERARQUIA: Validação detalhada pendente (requer análise de Execute Workflow nodes)');
  }

  // Calculate conformity score (0-100)
  calculateConformityScore() {
    let score = 100;
    
    this.validationResults.violations.forEach(violation => {
      switch (violation.severity) {
        case 'CRÍTICA':
          score -= 25;
          break;
        case 'ALTA':
          score -= 15;
          break;
        case 'MÉDIA':
          score -= 10;
          break;
        case 'BAIXA':
          score -= 5;
          break;
      }
    });

    this.validationResults.conformityScore = Math.max(0, score);
  }

  // Generate recommendations based on violations
  generateRecommendations() {
    this.validationResults.violations.forEach(violation => {
      switch (violation.type) {
        case 'WEBHOOK_MISSING':
          this.validationResults.recommendations.push('Configurar webhook no workflow Unified_User_Pipeline');
          break;
        case 'WEBHOOK_WRONG_LOCATION':
          this.validationResults.recommendations.push('Mover webhook para o workflow Unified_User_Pipeline');
          break;
        case 'MULTIPLE_WEBHOOKS':
          this.validationResults.recommendations.push('Remover webhooks de todos os workflows exceto Unified_User_Pipeline');
          break;
        case 'MAIN_WORKFLOW_MISSING':
          this.validationResults.recommendations.push('Criar workflow principal Unified_User_Pipeline');
          break;
        case 'MISSING_SUBFLOWS':
          this.validationResults.recommendations.push('Criar subfluxos faltantes conforme especificação');
          break;
      }
    });
  }

  // Display validation results
  displayResults() {
    console.log('\n📋 RESUMO DA VALIDAÇÃO ARQUITETURAL:');
    console.log(`Total de workflows: ${this.validationResults.totalWorkflows}`);
    console.log(`Workflows ativos: ${this.validationResults.activeWorkflows}`);
    console.log(`Workflows inativos: ${this.validationResults.inactiveWorkflows}`);
    console.log(`Subfluxos encontrados: ${this.validationResults.subflowsFound.length}/6`);
    console.log(`Score de conformidade: ${this.validationResults.conformityScore}/100`);

    if (this.validationResults.violations.length === 0) {
      console.log('\n🎉 ARQUITETURA CONFORME: Nenhuma violação detectada!');
    } else {
      console.log(`\n⚠️ VIOLAÇÕES DETECTADAS (${this.validationResults.violations.length}):`);
      this.validationResults.violations.forEach((violation, index) => {
        console.log(`${index + 1}. [${violation.severity}] ${violation.description}`);
        console.log(`   Impacto: ${violation.impact}`);
      });

      console.log(`\n💡 RECOMENDAÇÕES (${this.validationResults.recommendations.length}):`);
      this.validationResults.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }

  // Save validation report to file
  async saveValidationReport() {
    const reportPath = path.join(__dirname, '../../docs/architecture/ARCHITECTURE_VALIDATION.md');
    
    try {
      // Read current file to preserve existing content
      let existingContent = '';
      try {
        existingContent = await fs.readFile(reportPath, 'utf8');
      } catch (error) {
        // File doesn't exist, will create new one
      }

      // Generate new validation section
      const newValidationSection = this.generateValidationSection();

      // Update or append validation results
      const updatedContent = this.updateValidationDocument(existingContent, newValidationSection);

      await fs.writeFile(reportPath, updatedContent, 'utf8');
      console.log(`\n📄 Relatório salvo em: ${reportPath}`);
    } catch (error) {
      console.error('❌ Erro ao salvar relatório:', error.message);
    }
  }

  // Generate validation section for the document
  generateValidationSection() {
    const timestamp = new Date().toLocaleString('pt-BR');
    
    return `
## 🤖 VALIDAÇÃO ARQUITETURAL AUTOMÁTICA (Agent 7)

**Data da Validação**: ${timestamp}
**Score de Conformidade**: ${this.validationResults.conformityScore}/100
**Status**: ${this.validationResults.conformityScore >= 80 ? '✅ CONFORME' : '⚠️ NÃO CONFORME'}

### 📊 Estatísticas do Sistema
- **Total de workflows**: ${this.validationResults.totalWorkflows}
- **Workflows ativos**: ${this.validationResults.activeWorkflows}
- **Workflows inativos**: ${this.validationResults.inactiveWorkflows}
- **Subfluxos encontrados**: ${this.validationResults.subflowsFound.length}/6

### 🔍 Análise de Conformidade

#### Webhook Único (Regra Fundamental)
${this.validationResults.webhookWorkflows.length === 1 && this.validationResults.webhookWorkflows[0] === 'Unified_User_Pipeline' ? 
  '✅ **CONFORME**: Apenas Unified_User_Pipeline possui webhook' : 
  '❌ **VIOLAÇÃO**: ' + (this.validationResults.webhookWorkflows.length === 0 ? 'Nenhum webhook encontrado' : 
    this.validationResults.webhookWorkflows.length > 1 ? `Múltiplos webhooks: ${this.validationResults.webhookWorkflows.join(', ')}` :
    `Webhook em local incorreto: ${this.validationResults.webhookWorkflows[0]}`)}

#### Workflow Principal
${this.validationResults.unifiedPipelineFound ? 
  '✅ **CONFORME**: Unified_User_Pipeline encontrado' : 
  '❌ **VIOLAÇÃO**: Unified_User_Pipeline não encontrado'}

#### Subfluxos Especializados
${this.validationResults.subflowsFound.length === 6 ? 
  '✅ **CONFORME**: Todos os 6 subfluxos implementados' : 
  `⚠️ **PARCIAL**: ${this.validationResults.subflowsFound.length}/6 subfluxos encontrados`}

### ⚠️ Violações Detectadas (${this.validationResults.violations.length})

${this.validationResults.violations.map((v, i) => 
  `${i + 1}. **[${v.severity}]** ${v.description}\n   - Impacto: ${v.impact}`
).join('\n\n')}

### 💡 Recomendações de Correção (${this.validationResults.recommendations.length})

${this.validationResults.recommendations.map((r, i) => `${i + 1}. ${r}`).join('\n')}

---
`;
  }

  // Update validation document with new results
  updateValidationDocument(existingContent, newSection) {
    // Find existing validation section and replace it
    const validationSectionRegex = /## 🤖 VALIDAÇÃO ARQUITETURAL AUTOMÁTICA[\s\S]*?(?=##|$)/;
    
    if (validationSectionRegex.test(existingContent)) {
      return existingContent.replace(validationSectionRegex, newSection.trim() + '\n\n');
    } else {
      // Append to end of document
      return existingContent + '\n' + newSection;
    }
  }
}

// Main execution
async function main() {
  try {
    const validator = new ArchitectureValidator();
    await validator.validateArchitecture();
  } catch (error) {
    console.error('❌ Falha na validação arquitetural:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = ArchitectureValidator;
