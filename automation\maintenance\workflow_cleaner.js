#!/usr/bin/env node

// Workflow Cleaner - Agent 6
// <PERSON>pa e organiza todos os workflows n8n, removendo duplicações e estabelecendo hierarquia

const N8nApiClient = require('./n8n_api_client.js');

class WorkflowCleaner {
  constructor() {
    this.n8nUrl = 'https://n8n-n8n.w9jo16.easypanel.host';
    this.apiKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M';
    this.client = new N8nApiClient(this.n8nUrl, this.apiKey);
    
    // Padrões de nomenclatura corretos
    this.correctPatterns = {
      mainPipeline: 'Unified_User_Pipeline',
      subflows: [
        '01_Authentication_Decision',
        '02_Message_Processing',
        '03_AI_Response_Generation', 
        '04_Database_Operations',
        '05_Response_Delivery',
        '06_Error_Handling'
      ],
      utilities: [
        'Database_Manager',
        'Response_Matrix_Manager',
        'WhatsApp_API_Manager',
        'Supabase_Integration'
      ],
      maintenance: [
        'System_Monitor',
        'Backup_Manager',
        'Log_Analyzer'
      ]
    };
  }

  // Mapear todos os workflows existentes
  async mapAllWorkflows() {
    console.log('🔍 Mapeando todos os workflows existentes...\n');
    
    try {
      const response = await this.client.getWorkflows();
      const workflows = response.data || response;
      
      console.log(`📊 Total de workflows encontrados: ${workflows.length}\n`);
      
      const workflowMap = {
        active: [],
        inactive: [],
        duplicates: [],
        incorrectNaming: [],
        webhookViolations: [],
        obsolete: []
      };
      
      // Analisar cada workflow
      for (const workflow of workflows) {
        const analysis = await this.analyzeWorkflow(workflow);
        
        // Classificar workflow
        if (workflow.active) {
          workflowMap.active.push(analysis);
        } else {
          workflowMap.inactive.push(analysis);
        }
        
        // Verificar problemas
        if (analysis.isDuplicate) {
          workflowMap.duplicates.push(analysis);
        }
        
        if (analysis.hasIncorrectNaming) {
          workflowMap.incorrectNaming.push(analysis);
        }
        
        if (analysis.violatesWebhookPattern) {
          workflowMap.webhookViolations.push(analysis);
        }
        
        if (analysis.isObsolete) {
          workflowMap.obsolete.push(analysis);
        }
      }
      
      return workflowMap;
      
    } catch (error) {
      console.error('❌ Erro ao mapear workflows:', error);
      throw error;
    }
  }

  // Analisar um workflow específico
  async analyzeWorkflow(workflow) {
    const analysis = {
      id: workflow.id,
      name: workflow.name,
      active: workflow.active,
      createdAt: workflow.createdAt,
      updatedAt: workflow.updatedAt,
      nodeCount: workflow.nodes?.length || 0,
      hasWebhook: false,
      hasExecuteWorkflow: false,
      isDuplicate: false,
      hasIncorrectNaming: false,
      violatesWebhookPattern: false,
      isObsolete: false,
      category: 'unknown',
      issues: []
    };
    
    // Analisar nós do workflow
    if (workflow.nodes) {
      for (const node of workflow.nodes) {
        if (node.type === 'n8n-nodes-base.webhook') {
          analysis.hasWebhook = true;
        }
        if (node.type === 'n8n-nodes-base.executeWorkflow') {
          analysis.hasExecuteWorkflow = true;
        }
      }
    }
    
    // Verificar padrões de nomenclatura
    analysis.category = this.categorizeWorkflow(workflow.name);
    
    // Verificar violações do padrão webhook
    if (analysis.hasWebhook && workflow.name !== this.correctPatterns.mainPipeline) {
      analysis.violatesWebhookPattern = true;
      analysis.issues.push('Webhook em workflow que não é o Unified_User_Pipeline');
    }
    
    // Verificar nomenclatura incorreta
    if (this.hasIncorrectNaming(workflow.name)) {
      analysis.hasIncorrectNaming = true;
      analysis.issues.push('Nomenclatura não segue padrão estabelecido');
    }
    
    // Verificar se é duplicata (baseado no nome)
    analysis.isDuplicate = this.isDuplicateName(workflow.name);
    if (analysis.isDuplicate) {
      analysis.issues.push('Nome duplicado ou similar detectado');
    }
    
    // Verificar se é obsoleto
    analysis.isObsolete = this.isObsoleteWorkflow(workflow.name);
    if (analysis.isObsolete) {
      analysis.issues.push('Workflow obsoleto ou de teste');
    }
    
    return analysis;
  }

  // Categorizar workflow
  categorizeWorkflow(name) {
    if (name === this.correctPatterns.mainPipeline) {
      return 'main_pipeline';
    }
    
    if (this.correctPatterns.subflows.includes(name)) {
      return 'subflow';
    }
    
    if (this.correctPatterns.utilities.some(util => name.includes(util.replace('_', '')))) {
      return 'utility';
    }
    
    if (this.correctPatterns.maintenance.some(maint => name.includes(maint.replace('_', '')))) {
      return 'maintenance';
    }
    
    return 'unknown';
  }

  // Verificar nomenclatura incorreta
  hasIncorrectNaming(name) {
    // Padrões incorretos conhecidos
    const incorrectPatterns = [
      /^00_/,  // Prefixos numéricos incorretos
      /^01_.*webhook/i,  // Subfluxos com webhook
      /^02_.*webhook/i,
      /^03_.*webhook/i,
      /^04_.*webhook/i,
      /^05_.*webhook/i,
      /^06_.*webhook/i,
      /test/i,  // Workflows de teste
      /temp/i,  // Workflows temporários
      /backup/i,  // Backups antigos
      /copy/i,  // Cópias
      /duplicate/i  // Duplicatas
    ];
    
    return incorrectPatterns.some(pattern => pattern.test(name));
  }

  // Verificar se é nome duplicado
  isDuplicateName(name) {
    // Padrões que indicam duplicação
    const duplicatePatterns = [
      /\s+copy$/i,
      /\s+\(\d+\)$/,
      /\s+v\d+$/i,
      /\s+backup$/i,
      /_copy$/i,
      /_v\d+$/i,
      /_backup$/i
    ];
    
    return duplicatePatterns.some(pattern => pattern.test(name));
  }

  // Verificar se é workflow obsoleto
  isObsoleteWorkflow(name) {
    const obsoletePatterns = [
      /test/i,
      /temp/i,
      /old/i,
      /deprecated/i,
      /unused/i,
      /archive/i,
      /backup/i,
      /draft/i
    ];
    
    return obsoletePatterns.some(pattern => pattern.test(name));
  }

  // Gerar relatório detalhado
  generateReport(workflowMap) {
    console.log('📋 RELATÓRIO DE ANÁLISE DOS WORKFLOWS\n');
    console.log('=' .repeat(50));
    
    // Resumo geral
    console.log('\n📊 RESUMO GERAL:');
    console.log(`Total de workflows: ${workflowMap.active.length + workflowMap.inactive.length}`);
    console.log(`Ativos: ${workflowMap.active.length}`);
    console.log(`Inativos: ${workflowMap.inactive.length}`);
    
    // Problemas identificados
    console.log('\n🚨 PROBLEMAS IDENTIFICADOS:');
    console.log(`Duplicatas: ${workflowMap.duplicates.length}`);
    console.log(`Nomenclatura incorreta: ${workflowMap.incorrectNaming.length}`);
    console.log(`Violações de webhook: ${workflowMap.webhookViolations.length}`);
    console.log(`Workflows obsoletos: ${workflowMap.obsolete.length}`);
    
    // Detalhes dos problemas
    if (workflowMap.duplicates.length > 0) {
      console.log('\n🔄 WORKFLOWS DUPLICADOS:');
      workflowMap.duplicates.forEach(wf => {
        console.log(`  - ${wf.name} (ID: ${wf.id}) - ${wf.active ? 'ATIVO' : 'INATIVO'}`);
      });
    }
    
    if (workflowMap.webhookViolations.length > 0) {
      console.log('\n⚠️ VIOLAÇÕES DE WEBHOOK:');
      workflowMap.webhookViolations.forEach(wf => {
        console.log(`  - ${wf.name} (ID: ${wf.id}) - ${wf.active ? 'ATIVO' : 'INATIVO'}`);
      });
    }
    
    if (workflowMap.incorrectNaming.length > 0) {
      console.log('\n📝 NOMENCLATURA INCORRETA:');
      workflowMap.incorrectNaming.forEach(wf => {
        console.log(`  - ${wf.name} (ID: ${wf.id}) - ${wf.active ? 'ATIVO' : 'INATIVO'}`);
      });
    }
    
    if (workflowMap.obsolete.length > 0) {
      console.log('\n🗑️ WORKFLOWS OBSOLETOS:');
      workflowMap.obsolete.forEach(wf => {
        console.log(`  - ${wf.name} (ID: ${wf.id}) - ${wf.active ? 'ATIVO' : 'INATIVO'}`);
      });
    }
    
    console.log('\n' + '=' .repeat(50));
  }

  // Executar limpeza completa
  async executeCleanup(dryRun = true) {
    console.log('🧹 Iniciando limpeza dos workflows...\n');
    
    if (dryRun) {
      console.log('⚠️ MODO DRY RUN - Nenhuma alteração será feita\n');
    }
    
    const workflowMap = await this.mapAllWorkflows();
    this.generateReport(workflowMap);
    
    // Plano de limpeza
    const cleanupPlan = this.createCleanupPlan(workflowMap);
    this.displayCleanupPlan(cleanupPlan);
    
    if (!dryRun) {
      await this.executeCleanupPlan(cleanupPlan);
    }
    
    return cleanupPlan;
  }

  // Criar plano de limpeza
  createCleanupPlan(workflowMap) {
    const plan = {
      toDeactivate: [],
      toDelete: [],
      toRename: [],
      toKeep: []
    };
    
    // Workflows para desativar
    plan.toDeactivate = workflowMap.active.filter(wf => 
      wf.violatesWebhookPattern || wf.isObsolete || wf.isDuplicate
    );
    
    // Workflows para deletar (inativos com problemas)
    plan.toDelete = workflowMap.inactive.filter(wf => 
      wf.isObsolete || wf.isDuplicate
    );
    
    // Workflows para renomear
    plan.toRename = workflowMap.active.filter(wf => 
      wf.hasIncorrectNaming && !wf.violatesWebhookPattern && !wf.isObsolete
    );
    
    // Workflows para manter
    plan.toKeep = workflowMap.active.filter(wf => 
      !wf.violatesWebhookPattern && !wf.isObsolete && !wf.isDuplicate && !wf.hasIncorrectNaming
    );
    
    return plan;
  }

  // Exibir plano de limpeza
  displayCleanupPlan(plan) {
    console.log('\n📋 PLANO DE LIMPEZA:\n');
    
    console.log(`🔴 Para DESATIVAR (${plan.toDeactivate.length}):`);
    plan.toDeactivate.forEach(wf => {
      console.log(`  - ${wf.name} (${wf.issues.join(', ')})`);
    });
    
    console.log(`\n🗑️ Para DELETAR (${plan.toDelete.length}):`);
    plan.toDelete.forEach(wf => {
      console.log(`  - ${wf.name} (${wf.issues.join(', ')})`);
    });
    
    console.log(`\n✏️ Para RENOMEAR (${plan.toRename.length}):`);
    plan.toRename.forEach(wf => {
      console.log(`  - ${wf.name} → ${this.suggestCorrectName(wf.name)}`);
    });
    
    console.log(`\n✅ Para MANTER (${plan.toKeep.length}):`);
    plan.toKeep.forEach(wf => {
      console.log(`  - ${wf.name}`);
    });
  }

  // Sugerir nome correto
  suggestCorrectName(currentName) {
    // Lógica para sugerir nomes corretos baseados nos padrões
    if (currentName.includes('auth')) return '01_Authentication_Decision';
    if (currentName.includes('message') || currentName.includes('process')) return '02_Message_Processing';
    if (currentName.includes('ai') || currentName.includes('response')) return '03_AI_Response_Generation';
    if (currentName.includes('database') || currentName.includes('db')) return '04_Database_Operations';
    if (currentName.includes('delivery') || currentName.includes('send')) return '05_Response_Delivery';
    if (currentName.includes('error') || currentName.includes('handle')) return '06_Error_Handling';
    
    return currentName.replace(/^[0-9]+_/, '').replace(/[^a-zA-Z0-9_]/g, '_');
  }

  // Executar plano de limpeza
  async executeCleanupPlan(plan) {
    console.log('\n🚀 Executando plano de limpeza...\n');
    
    // Desativar workflows
    for (const workflow of plan.toDeactivate) {
      try {
        await this.client.deactivateWorkflow(workflow.id);
        console.log(`✅ Desativado: ${workflow.name}`);
      } catch (error) {
        console.error(`❌ Erro ao desativar ${workflow.name}:`, error.message);
      }
    }
    
    // Deletar workflows
    for (const workflow of plan.toDelete) {
      try {
        await this.client.deleteWorkflow(workflow.id);
        console.log(`🗑️ Deletado: ${workflow.name}`);
      } catch (error) {
        console.error(`❌ Erro ao deletar ${workflow.name}:`, error.message);
      }
    }
    
    console.log('\n✅ Limpeza concluída!');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const cleaner = new WorkflowCleaner();
  
  // Verificar argumentos da linha de comando
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  
  cleaner.executeCleanup(dryRun)
    .then(plan => {
      if (dryRun) {
        console.log('\n💡 Para executar a limpeza real, use: node workflow_cleaner.js --execute');
      }
    })
    .catch(error => {
      console.error('❌ Erro na limpeza:', error);
      process.exit(1);
    });
}

module.exports = WorkflowCleaner;
