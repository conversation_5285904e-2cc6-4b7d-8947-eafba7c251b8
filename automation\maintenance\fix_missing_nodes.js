#!/usr/bin/env node

/**
 * Missing Node Types Fix Script
 * 
 * This script identifies workflows with missing or unsupported node types
 * and replaces them with compatible alternatives.
 * 
 * Usage: node automation/fix_missing_nodes.js
 */

require('dotenv').config();
const N8nApiClient = require('./n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL,
  n8nApiKey: process.env.N8N_API_KEY,
  evolutionApiUrl: process.env.EVOLUTION_API_URL,
  evolutionApiKey: process.env.EVOLUTION_API_KEY,
  evolutionInstance: process.env.EVOLUTION_INSTANCE,
  openaiApiKey: process.env.OPENAI_API_KEY
};

// Node type replacements for missing/unsupported nodes
const NODE_REPLACEMENTS = {
  // AI/OpenAI nodes
  'n8n-nodes-base.openAi': {
    type: 'n8n-nodes-base.httpRequest',
    name: 'OpenAI API Request',
    parameters: {
      url: 'https://api.openai.com/v1/chat/completions',
      authentication: 'predefinedCredentialType',
      nodeCredentialType: 'httpHeaderAuth',
      sendBody: true,
      specifyBody: 'json',
      jsonBody: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: '{{ $json.message || $json.prompt }}' }],
        max_tokens: 1000,
        temperature: 0.7
      }, null, 2)
    }
  },
  
  // Vector store nodes
  'n8n-nodes-base.vectorStore': {
    type: 'n8n-nodes-base.httpRequest',
    name: 'Vector Store Request',
    parameters: {
      url: '{{ $env.VECTOR_STORE_URL || "http://localhost:8080" }}/query',
      sendBody: true,
      specifyBody: 'json',
      jsonBody: '{{ JSON.stringify($json) }}'
    }
  },

  // Supabase nodes
  'n8n-nodes-base.supabase': {
    type: 'n8n-nodes-base.httpRequest',
    name: 'Supabase Request',
    parameters: {
      url: '{{ $env.SUPABASE_URL }}/rest/v1/{{ $parameter.table }}',
      authentication: 'predefinedCredentialType',
      nodeCredentialType: 'httpHeaderAuth',
      sendBody: true,
      specifyBody: 'json'
    }
  },

  // Custom nodes that might not be available
  'n8n-nodes-base.customNode': {
    type: 'n8n-nodes-base.code',
    name: 'Custom Logic',
    parameters: {
      mode: 'runOnceForAllItems',
      jsCode: '// Custom node logic\nreturn items;'
    }
  }
};

class MissingNodeFixer {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.fixResults = [];
  }

  // Main fix process
  async fixAllMissingNodes() {
    console.log('🔧 Starting Missing Node Types Fix Process\n');
    console.log('🎯 Target n8n Instance:', CONFIG.n8nApiUrl);
    console.log('');

    try {
      // Get all workflows
      const workflows = await this.client.getWorkflows();
      console.log(`📋 Found ${workflows.data?.length || 0} workflows to check\n`);

      // Check each workflow for missing nodes
      for (const workflow of workflows.data || []) {
        await this.fixWorkflowNodes(workflow);
      }

      // Generate report
      await this.generateFixReport();

      const successCount = this.fixResults.filter(r => r.status === 'FIXED').length;
      const errorCount = this.fixResults.filter(r => r.status === 'ERROR').length;

      console.log(`\n✅ Missing node fix completed!`);
      console.log(`📊 Fixed: ${successCount} workflows`);
      console.log(`❌ Errors: ${errorCount} workflows`);

      return errorCount === 0;

    } catch (error) {
      console.error('❌ Missing node fix failed:', error.message);
      return false;
    }
  }

  // Fix nodes in a specific workflow
  async fixWorkflowNodes(workflow) {
    console.log(`🔍 Checking workflow: ${workflow.name}`);

    try {
      // Get full workflow details
      const fullWorkflow = await this.client.getWorkflow(workflow.id);
      let hasChanges = false;
      const fixes = [];

      // Check each node
      for (let node of fullWorkflow.nodes || []) {
        const nodeFixes = this.fixNode(node);
        if (nodeFixes.length > 0) {
          hasChanges = true;
          fixes.push(...nodeFixes);
        }
      }

      // Update workflow if changes were made
      if (hasChanges) {
        await this.client.updateWorkflow(workflow.id, fullWorkflow);
        console.log(`  ✅ Applied ${fixes.length} fixes to ${workflow.name}`);
        
        this.fixResults.push({
          workflow: workflow.name,
          workflowId: workflow.id,
          fixes: fixes,
          status: 'FIXED'
        });
      } else {
        console.log(`  ✓ No missing nodes in ${workflow.name}`);
        
        this.fixResults.push({
          workflow: workflow.name,
          workflowId: workflow.id,
          fixes: [],
          status: 'OK'
        });
      }

    } catch (error) {
      console.error(`  ❌ Failed to fix ${workflow.name}:`, error.message);
      
      this.fixResults.push({
        workflow: workflow.name,
        workflowId: workflow.id,
        fixes: [],
        status: 'ERROR',
        error: error.message
      });
    }
  }

  // Fix individual node
  fixNode(node) {
    const fixes = [];

    // Check if node type needs replacement
    if (NODE_REPLACEMENTS[node.type]) {
      const replacement = NODE_REPLACEMENTS[node.type];
      const oldType = node.type;
      
      // Replace node type and parameters
      node.type = replacement.type;
      node.parameters = { ...node.parameters, ...replacement.parameters };
      
      fixes.push(`Replaced ${oldType} with ${replacement.type} in node ${node.name}`);
    }

    // Fix specific node configurations
    fixes.push(...this.fixNodeSpecificIssues(node));

    return fixes;
  }

  // Fix specific node configuration issues
  fixNodeSpecificIssues(node) {
    const fixes = [];

    // Fix HTTP Request nodes with Evolution API
    if (node.type === 'n8n-nodes-base.httpRequest' && this.isEvolutionApiNode(node)) {
      fixes.push(...this.fixEvolutionApiNode(node));
    }

    // Fix Code nodes with missing functions
    if (node.type === 'n8n-nodes-base.code') {
      fixes.push(...this.fixCodeNode(node));
    }

    // Fix IF nodes with invalid conditions
    if (node.type === 'n8n-nodes-base.if') {
      fixes.push(...this.fixIfNode(node));
    }

    // Fix Set nodes with invalid expressions
    if (node.type === 'n8n-nodes-base.set') {
      fixes.push(...this.fixSetNode(node));
    }

    return fixes;
  }

  // Check if node is Evolution API related
  isEvolutionApiNode(node) {
    if (!node.parameters?.url) return false;
    const url = node.parameters.url;
    return url.includes('evolution') || url.includes('$env.EVOLUTION_API_URL');
  }

  // Fix Evolution API node
  fixEvolutionApiNode(node) {
    const fixes = [];

    // Ensure proper URL format
    if (node.parameters.url && !node.parameters.url.includes('{{')) {
      if (node.parameters.url.includes('/message/sendText')) {
        node.parameters.url = `{{ $env.EVOLUTION_API_URL }}/message/sendText/{{ $env.EVOLUTION_INSTANCE }}`;
        fixes.push(`Fixed Evolution API URL format in ${node.name}`);
      }
    }

    // Ensure proper authentication
    if (!node.parameters.authentication) {
      node.parameters.authentication = 'genericCredentialType';
      node.parameters.genericAuthType = 'httpHeaderAuth';
      node.parameters.httpHeaderAuth = {
        name: 'apikey',
        value: '{{ $env.EVOLUTION_API_KEY }}'
      };
      fixes.push(`Added Evolution API authentication to ${node.name}`);
    }

    return fixes;
  }

  // Fix Code node issues
  fixCodeNode(node) {
    const fixes = [];

    if (!node.parameters?.jsCode) {
      node.parameters = node.parameters || {};
      node.parameters.jsCode = '// Default code\nreturn items;';
      fixes.push(`Added default code to ${node.name}`);
    }

    return fixes;
  }

  // Fix IF node issues
  fixIfNode(node) {
    const fixes = [];

    if (!node.parameters?.conditions) {
      node.parameters = node.parameters || {};
      node.parameters.conditions = {
        options: {
          caseSensitive: true,
          leftValue: '',
          typeValidation: 'strict'
        },
        conditions: [
          {
            leftValue: '{{ $json.value }}',
            rightValue: '',
            operator: {
              type: 'string',
              operation: 'equals'
            }
          }
        ],
        combinator: 'and'
      };
      fixes.push(`Added default conditions to ${node.name}`);
    }

    return fixes;
  }

  // Fix Set node issues
  fixSetNode(node) {
    const fixes = [];

    if (!node.parameters?.values) {
      node.parameters = node.parameters || {};
      node.parameters.values = {
        values: [
          {
            name: 'output',
            value: '{{ $json }}'
          }
        ]
      };
      fixes.push(`Added default values to ${node.name}`);
    }

    return fixes;
  }

  // Generate fix report
  async generateFixReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = `missing_nodes_fix_report_${timestamp}.json`;

    const report = {
      timestamp: new Date().toISOString(),
      n8nInstance: CONFIG.n8nApiUrl,
      totalWorkflows: this.fixResults.length,
      fixedWorkflows: this.fixResults.filter(r => r.status === 'FIXED').length,
      errorWorkflows: this.fixResults.filter(r => r.status === 'ERROR').length,
      okWorkflows: this.fixResults.filter(r => r.status === 'OK').length,
      nodeReplacements: NODE_REPLACEMENTS,
      results: this.fixResults
    };

    const fs = require('fs').promises;
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log(`\n📊 Missing Nodes Fix Report: ${reportPath}`);
    console.log(`   ✅ Fixed: ${report.fixedWorkflows} workflows`);
    console.log(`   ✓ OK: ${report.okWorkflows} workflows`);
    console.log(`   ❌ Errors: ${report.errorWorkflows} workflows`);
  }
}

// Main execution
async function main() {
  if (!CONFIG.n8nApiKey) {
    console.error('❌ N8N_API_KEY environment variable is required');
    process.exit(1);
  }

  const fixer = new MissingNodeFixer();
  const success = await fixer.fixAllMissingNodes();
  
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = MissingNodeFixer;
