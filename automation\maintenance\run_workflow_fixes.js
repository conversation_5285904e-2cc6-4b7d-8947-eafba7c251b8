#!/usr/bin/env node

/**
 * Comprehensive Workflow Fix Runner
 * 
 * This script orchestrates all the necessary fixes for the deployed n8n workflows:
 * 1. Validates environment configuration
 * 2. Fixes workflow node configurations
 * 3. Configures OpenAI credentials
 * 4. Tests the fixed workflows
 * 
 * Usage: node automation/run_workflow_fixes.js
 */

require('dotenv').config();
const WorkflowConfigurationFixer = require('./fix_workflow_configurations.js');
const OpenAICredentialConfigurator = require('./configure_openai_credentials.js');

// Configuration validation
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL,
  n8nApiKey: process.env.N8N_API_KEY,
  evolutionApiUrl: process.env.EVOLUTION_API_URL,
  evolutionApiKey: process.env.EVOLUTION_API_KEY,
  evolutionInstance: process.env.EVOLUTION_INSTANCE,
  openaiApiKey: process.env.OPENAI_API_KEY
};

class WorkflowFixRunner {
  constructor() {
    this.results = {
      validation: null,
      workflowFixes: null,
      credentialConfig: null,
      testing: null,
      overall: 'PENDING'
    };
  }

  // Main execution flow
  async runAllFixes() {
    console.log('🔧 Starting Comprehensive Workflow Fix Process\n');
    console.log('=' .repeat(60));
    console.log('🎯 Target n8n Instance:', CONFIG.n8nApiUrl);
    console.log('🌐 Evolution API:', CONFIG.evolutionApiUrl);
    console.log('🤖 Evolution Instance:', CONFIG.evolutionInstance);
    console.log('=' .repeat(60));
    console.log('');

    try {
      // Step 1: Validate environment
      console.log('📋 STEP 1: Environment Validation');
      console.log('-' .repeat(40));
      this.results.validation = await this.validateEnvironment();
      
      if (!this.results.validation.success) {
        throw new Error('Environment validation failed');
      }
      console.log('✅ Environment validation passed\n');

      // Step 2: Fix workflow configurations
      console.log('🔧 STEP 2: Fix Workflow Configurations');
      console.log('-' .repeat(40));
      this.results.workflowFixes = await this.fixWorkflowConfigurations();
      console.log('✅ Workflow configuration fixes completed\n');

      // Step 3: Configure credentials
      console.log('🔑 STEP 3: Configure OpenAI Credentials');
      console.log('-' .repeat(40));
      this.results.credentialConfig = await this.configureCredentials();
      console.log('✅ Credential configuration completed\n');

      // Step 4: Test workflows
      console.log('🧪 STEP 4: Test Fixed Workflows');
      console.log('-' .repeat(40));
      this.results.testing = await this.testWorkflows();
      console.log('✅ Workflow testing completed\n');

      // Generate final report
      await this.generateFinalReport();

      this.results.overall = 'SUCCESS';
      console.log('🎉 All workflow fixes completed successfully!');
      return true;

    } catch (error) {
      console.error('❌ Workflow fix process failed:', error.message);
      this.results.overall = 'FAILED';
      this.results.error = error.message;
      
      await this.generateFinalReport();
      return false;
    }
  }

  // Validate environment configuration
  async validateEnvironment() {
    const validation = {
      success: true,
      issues: [],
      warnings: []
    };

    // Check required environment variables
    const requiredVars = [
      { name: 'N8N_API_URL', value: CONFIG.n8nApiUrl },
      { name: 'N8N_API_KEY', value: CONFIG.n8nApiKey },
      { name: 'EVOLUTION_API_URL', value: CONFIG.evolutionApiUrl },
      { name: 'EVOLUTION_API_KEY', value: CONFIG.evolutionApiKey },
      { name: 'EVOLUTION_INSTANCE', value: CONFIG.evolutionInstance },
      { name: 'OPENAI_API_KEY', value: CONFIG.openaiApiKey }
    ];

    for (const envVar of requiredVars) {
      if (!envVar.value) {
        validation.issues.push(`Missing required environment variable: ${envVar.name}`);
        validation.success = false;
      } else {
        console.log(`  ✓ ${envVar.name}: ${envVar.value.substring(0, 20)}...`);
      }
    }

    // Test n8n API connectivity
    try {
      const N8nApiClient = require('./n8n_api_client.js');
      const client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
      await client.getWorkflows();
      console.log('  ✓ n8n API connectivity: OK');
    } catch (error) {
      validation.issues.push(`n8n API connectivity failed: ${error.message}`);
      validation.success = false;
    }

    // Test Evolution API connectivity
    try {
      const fetch = require('node-fetch');
      const response = await fetch(`${CONFIG.evolutionApiUrl}/instance/fetchInstances`, {
        method: 'GET',
        headers: {
          'apikey': CONFIG.evolutionApiKey,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        console.log('  ✓ Evolution API connectivity: OK');
      } else {
        validation.warnings.push(`Evolution API returned status ${response.status}`);
      }
    } catch (error) {
      validation.warnings.push(`Evolution API connectivity test failed: ${error.message}`);
    }

    // Test OpenAI API connectivity
    try {
      const fetch = require('node-fetch');
      const response = await fetch('https://api.openai.com/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${CONFIG.openaiApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        console.log('  ✓ OpenAI API connectivity: OK');
      } else {
        validation.warnings.push(`OpenAI API returned status ${response.status}`);
      }
    } catch (error) {
      validation.warnings.push(`OpenAI API connectivity test failed: ${error.message}`);
    }

    // Display warnings
    if (validation.warnings.length > 0) {
      console.log('\n  ⚠️  Warnings:');
      validation.warnings.forEach(warning => console.log(`    - ${warning}`));
    }

    // Display issues
    if (validation.issues.length > 0) {
      console.log('\n  ❌ Issues:');
      validation.issues.forEach(issue => console.log(`    - ${issue}`));
    }

    return validation;
  }

  // Fix workflow configurations
  async fixWorkflowConfigurations() {
    try {
      const fixer = new WorkflowConfigurationFixer();
      const success = await fixer.fixAllWorkflows();
      
      return {
        success: success,
        fixLog: fixer.fixLog || []
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Configure credentials
  async configureCredentials() {
    try {
      const configurator = new OpenAICredentialConfigurator();
      const success = await configurator.configureCredentials();
      
      return {
        success: success,
        message: success ? 'OpenAI credentials configured' : 'Manual configuration required'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Manual configuration required'
      };
    }
  }

  // Test workflows
  async testWorkflows() {
    try {
      console.log('  🔍 Testing workflow connectivity...');
      
      const N8nApiClient = require('./n8n_api_client.js');
      const client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
      
      // Get all workflows
      const workflows = await client.getWorkflows();
      const activeWorkflows = workflows.data?.filter(w => w.active) || [];
      
      console.log(`  📊 Found ${workflows.data?.length || 0} total workflows`);
      console.log(`  ✅ Found ${activeWorkflows.length} active workflows`);
      
      // Check specific workflows
      const expectedWorkflows = [
        'WhatsApp Message Router v1',
        'Main Orchestrator Agent v2',
        'Message Processor v1',
        'SuperUser Authentication v1',
        'Session Manager v1'
      ];

      const foundWorkflows = [];
      const missingWorkflows = [];

      for (const expectedName of expectedWorkflows) {
        const found = workflows.data?.find(w => w.name === expectedName);
        if (found) {
          foundWorkflows.push({
            name: expectedName,
            id: found.id,
            active: found.active
          });
          console.log(`  ✓ Found: ${expectedName} (${found.active ? 'Active' : 'Inactive'})`);
        } else {
          missingWorkflows.push(expectedName);
          console.log(`  ❌ Missing: ${expectedName}`);
        }
      }

      return {
        success: missingWorkflows.length === 0,
        totalWorkflows: workflows.data?.length || 0,
        activeWorkflows: activeWorkflows.length,
        foundWorkflows: foundWorkflows,
        missingWorkflows: missingWorkflows
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Generate final report
  async generateFinalReport() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = `comprehensive_fix_report_${timestamp}.json`;

    const report = {
      timestamp: new Date().toISOString(),
      configuration: {
        n8nInstance: CONFIG.n8nApiUrl,
        evolutionApiUrl: CONFIG.evolutionApiUrl,
        evolutionInstance: CONFIG.evolutionInstance
      },
      results: this.results,
      summary: {
        overall: this.results.overall,
        environmentValidation: this.results.validation?.success ? 'PASSED' : 'FAILED',
        workflowFixes: this.results.workflowFixes?.success ? 'COMPLETED' : 'FAILED',
        credentialConfig: this.results.credentialConfig?.success ? 'COMPLETED' : 'MANUAL_REQUIRED',
        workflowTesting: this.results.testing?.success ? 'PASSED' : 'ISSUES_FOUND'
      },
      nextSteps: this.generateNextSteps()
    };

    const fs = require('fs').promises;
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log('\n📊 COMPREHENSIVE FIX REPORT');
    console.log('=' .repeat(60));
    console.log(`📄 Report saved to: ${reportPath}`);
    console.log(`🎯 Overall Status: ${report.summary.overall}`);
    console.log(`🔍 Environment: ${report.summary.environmentValidation}`);
    console.log(`🔧 Workflow Fixes: ${report.summary.workflowFixes}`);
    console.log(`🔑 Credentials: ${report.summary.credentialConfig}`);
    console.log(`🧪 Testing: ${report.summary.workflowTesting}`);
    
    if (report.nextSteps.length > 0) {
      console.log('\n📋 NEXT STEPS:');
      report.nextSteps.forEach((step, index) => {
        console.log(`${index + 1}. ${step}`);
      });
    }
  }

  // Generate next steps based on results
  generateNextSteps() {
    const steps = [];

    if (!this.results.validation?.success) {
      steps.push('Fix environment configuration issues listed in the validation report');
    }

    if (!this.results.workflowFixes?.success) {
      steps.push('Review workflow fix errors and manually correct problematic nodes');
    }

    if (!this.results.credentialConfig?.success) {
      steps.push('Manually configure OpenAI credentials in n8n interface (Settings > Credentials)');
    }

    if (this.results.testing?.missingWorkflows?.length > 0) {
      steps.push('Deploy missing workflows: ' + this.results.testing.missingWorkflows.join(', '));
    }

    if (this.results.testing?.foundWorkflows?.some(w => !w.active)) {
      steps.push('Activate inactive workflows in n8n interface');
    }

    if (steps.length === 0) {
      steps.push('Test the complete WhatsApp message processing pipeline');
      steps.push('Monitor workflow executions for any runtime errors');
    }

    return steps;
  }
}

// Main execution
async function main() {
  const runner = new WorkflowFixRunner();
  const success = await runner.runAllFixes();
  
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = WorkflowFixRunner;
