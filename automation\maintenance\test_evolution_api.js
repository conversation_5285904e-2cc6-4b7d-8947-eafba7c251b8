#!/usr/bin/env node

/**
 * Evolution API Connection Tester
 * 
 * Tests the connection to your Evolution API instance and validates
 * that WhatsApp integration is working properly.
 */

require('dotenv').config();

class EvolutionApiTester {
  constructor() {
    this.apiUrl = process.env.EVOLUTION_API_URL;
    this.apiKey = process.env.EVOLUTION_API_KEY;
    this.instanceName = process.env.EVOLUTION_INSTANCE;
    
    if (!this.apiUrl || !this.apiKey || !this.instanceName) {
      throw new Error('Missing Evolution API configuration. Please check your .env file.');
    }
  }

  // Test basic API connection
  async testApiConnection() {
    console.log('🔗 Testing Evolution API connection...');
    
    try {
      const response = await fetch(`${this.apiUrl}/instance/fetchInstances`, {
        method: 'GET',
        headers: {
          'apikey': this.apiKey,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ API Connection successful');
      console.log(`📊 Found ${data.length || 0} instances`);
      
      return { success: true, data };
      
    } catch (error) {
      console.error('❌ API Connection failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  // Test specific instance status
  async testInstanceStatus() {
    console.log(`📱 Testing instance: ${this.instanceName}...`);
    
    try {
      const response = await fetch(`${this.apiUrl}/instance/connectionState/${this.instanceName}`, {
        method: 'GET',
        headers: {
          'apikey': this.apiKey,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ Instance status retrieved');
      console.log(`📊 State: ${data.instance?.state || 'unknown'}`);
      console.log(`📊 Status: ${data.instance?.status || 'unknown'}`);
      
      return { success: true, data };
      
    } catch (error) {
      console.error('❌ Instance status check failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  // Test webhook configuration
  async testWebhookConfig() {
    console.log('🌐 Testing webhook configuration...');
    
    try {
      const response = await fetch(`${this.apiUrl}/webhook/find/${this.instanceName}`, {
        method: 'GET',
        headers: {
          'apikey': this.apiKey,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ Webhook configuration retrieved');

      // Check if webhook data exists (Evolution API v2.x format)
      if (data && data.url) {
        console.log(`📊 Webhook URL: ${data.url}`);
        console.log(`📊 Status: ${data.enabled ? 'Enabled' : 'Disabled'}`);
        console.log(`📊 Events: ${data.events?.length || 0} events configured`);
        console.log(`📊 Events List: ${data.events?.slice(0, 5).join(', ')}${data.events?.length > 5 ? '...' : ''}`);
        console.log(`📊 Webhook By Events: ${data.webhookByEvents}`);
        console.log(`📊 Base64 Encoding: ${data.webhookBase64}`);
      } else if (data.webhook) {
        // Legacy format check
        console.log(`📊 Webhook URL: ${data.webhook.url || 'Not configured'}`);
        console.log(`📊 Events: ${data.webhook.events?.join(', ') || 'None'}`);
      } else {
        console.log('⚠️ No webhook configured');
      }
      
      return { success: true, data };
      
    } catch (error) {
      console.error('❌ Webhook check failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  // Test sending a message (to yourself for testing)
  async testSendMessage(testPhoneNumber) {
    console.log(`📤 Testing message sending to ${testPhoneNumber}...`);
    
    try {
      const response = await fetch(`${this.apiUrl}/message/sendText/${this.instanceName}`, {
        method: 'POST',
        headers: {
          'apikey': this.apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          number: testPhoneNumber,
          text: '🤖 Test message from WhatsApp AI Assistant - Connection successful!'
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ Test message sent successfully');
      console.log(`📊 Message ID: ${data.key?.id || 'unknown'}`);
      
      return { success: true, data };
      
    } catch (error) {
      console.error('❌ Message sending failed:', error.message);
      return { success: false, error: error.message };
    }
  }

  // Configure webhook for n8n with multiple API version support
  async configureWebhook(n8nWebhookUrl) {
    console.log(`🔧 Configuring webhook: ${n8nWebhookUrl}...`);

    // Try different webhook configuration formats for different Evolution API versions
    const webhookConfigs = [
      // Configuration 1: Standard format
      {
        name: 'Standard Format',
        endpoint: `/webhook/set/${this.instanceName}`,
        payload: {
          url: n8nWebhookUrl,
          events: ['MESSAGES_UPSERT', 'MESSAGE_RECEIVED'],
          webhook_by_events: false
        }
      },
      // Configuration 2: Alternative format
      {
        name: 'Alternative Format',
        endpoint: `/webhook/set/${this.instanceName}`,
        payload: {
          url: n8nWebhookUrl,
          events: ['message', 'message.upsert'],
          enabled: true
        }
      },
      // Configuration 3: Simplified format
      {
        name: 'Simplified Format',
        endpoint: `/webhook/set/${this.instanceName}`,
        payload: {
          url: n8nWebhookUrl,
          webhook_by_events: false
        }
      },
      // Configuration 4: Legacy format
      {
        name: 'Legacy Format',
        endpoint: `/webhook/${this.instanceName}`,
        payload: {
          url: n8nWebhookUrl,
          events: ['message']
        }
      }
    ];

    for (const config of webhookConfigs) {
      console.log(`🔄 Trying ${config.name}...`);

      try {
        const response = await fetch(`${this.apiUrl}${config.endpoint}`, {
          method: 'POST',
          headers: {
            'apikey': this.apiKey,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(config.payload)
        });

        const responseText = await response.text();
        let data;

        try {
          data = JSON.parse(responseText);
        } catch {
          data = { raw_response: responseText };
        }

        if (response.ok) {
          console.log(`✅ Webhook configured successfully with ${config.name}`);
          console.log(`📊 Response:`, JSON.stringify(data, null, 2));
          return { success: true, data, config_used: config.name };
        } else {
          console.log(`❌ ${config.name} failed: HTTP ${response.status}`);
          console.log(`📊 Response:`, responseText);
        }

      } catch (error) {
        console.log(`❌ ${config.name} error:`, error.message);
      }
    }

    console.error('❌ All webhook configuration attempts failed');
    return { success: false, error: 'All webhook configuration formats failed' };
  }

  // Get detailed API information for debugging
  async getApiInfo() {
    console.log('🔍 Getting Evolution API information...');

    try {
      const endpoints = [
        '/instance/fetchInstances',
        '/webhook/find/' + this.instanceName,
        '/'
      ];

      const results = {};

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(`${this.apiUrl}${endpoint}`, {
            method: 'GET',
            headers: {
              'apikey': this.apiKey,
              'Content-Type': 'application/json'
            }
          });

          const data = await response.json();
          results[endpoint] = {
            status: response.status,
            data: data
          };

        } catch (error) {
          results[endpoint] = {
            error: error.message
          };
        }
      }

      console.log('📊 API Information:', JSON.stringify(results, null, 2));
      return { success: true, data: results };

    } catch (error) {
      console.error('❌ Failed to get API info:', error.message);
      return { success: false, error: error.message };
    }
  }

  // Run all tests
  async runAllTests(options = {}) {
    console.log('🧪 Evolution API Test Suite\n');
    console.log(`🎯 API URL: ${this.apiUrl}`);
    console.log(`📱 Instance: ${this.instanceName}`);
    console.log('');

    const results = {};

    // Test 0: Get API Information for debugging
    if (options.debug) {
      results.apiInfo = await this.getApiInfo();
      console.log('');
    }

    // Test 1: API Connection
    results.apiConnection = await this.testApiConnection();
    console.log('');

    // Test 2: Instance Status
    results.instanceStatus = await this.testInstanceStatus();
    console.log('');

    // Test 3: Webhook Configuration
    results.webhookConfig = await this.testWebhookConfig();
    console.log('');

    // Test 4: Configure webhook if provided and not already configured
    if (options.n8nWebhookUrl) {
      // Check if webhook is already configured correctly
      const currentWebhook = results.webhookConfig;
      let needsConfiguration = true;

      if (currentWebhook.success && currentWebhook.data) {
        const webhookData = currentWebhook.data;
        // Check if webhook URL matches and is enabled
        if (webhookData.url === options.n8nWebhookUrl && webhookData.enabled) {
          console.log('✅ Webhook already configured correctly - skipping reconfiguration');
          results.webhookSetup = {
            success: true,
            message: 'Webhook already configured correctly',
            data: webhookData
          };
          needsConfiguration = false;
        }
      }

      if (needsConfiguration) {
        results.webhookSetup = await this.configureWebhook(options.n8nWebhookUrl);
      }
      console.log('');
    }

    // Test 5: Send test message if phone provided
    if (options.testPhoneNumber) {
      results.messageSending = await this.testSendMessage(options.testPhoneNumber);
      console.log('');
    }

    // Generate summary
    const summary = this.generateTestSummary(results);
    this.displayResults(summary);

    return { results, summary };
  }

  generateTestSummary(results) {
    const tests = Object.keys(results);
    const passed = tests.filter(test => results[test].success).length;
    const failed = tests.filter(test => !results[test].success).length;
    
    return {
      total: tests.length,
      passed,
      failed,
      success_rate: ((passed / tests.length) * 100).toFixed(1) + '%',
      timestamp: new Date().toISOString()
    };
  }

  displayResults(summary) {
    console.log('📊 Evolution API Test Results');
    console.log('=' .repeat(40));
    console.log(`📋 Total Tests: ${summary.total}`);
    console.log(`✅ Passed: ${summary.passed}`);
    console.log(`❌ Failed: ${summary.failed}`);
    console.log(`📈 Success Rate: ${summary.success_rate}`);
    console.log('=' .repeat(40));
    
    if (summary.failed > 0) {
      console.log('\n⚠️ Some tests failed. Please check your Evolution API configuration.');
      console.log('💡 Make sure your Evolution API instance is running and properly configured.');
    } else {
      console.log('\n🎉 All tests passed! Your Evolution API is ready for WhatsApp integration.');
    }
  }
}

// CLI execution
if (require.main === module) {
  const tester = new EvolutionApiTester();

  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};

  // Look for webhook URL argument
  const webhookIndex = args.indexOf('--webhook');
  if (webhookIndex !== -1 && args[webhookIndex + 1]) {
    options.n8nWebhookUrl = args[webhookIndex + 1];
  }

  // Look for test phone number argument
  const phoneIndex = args.indexOf('--phone');
  if (phoneIndex !== -1 && args[phoneIndex + 1]) {
    options.testPhoneNumber = args[phoneIndex + 1];
  }

  // Look for debug flag
  if (args.includes('--debug')) {
    options.debug = true;
  }

  // Show help if requested
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🤖 Evolution API Tester - Usage:

Basic connection test:
  node automation/test_evolution_api.js

Test with webhook configuration:
  node automation/test_evolution_api.js --webhook "https://your-n8n-instance.com/webhook/whatsapp"

Test with message sending:
  node automation/test_evolution_api.js --phone "5511999999999"

Debug mode (shows detailed API information):
  node automation/test_evolution_api.js --debug

Full test with all options:
  node automation/test_evolution_api.js --webhook "https://n8n-n8n.w9jo16.easypanel.host/webhook/assistente_v0.1" --phone "5511999999999" --debug

Options:
  --webhook <url>    Configure webhook URL for n8n integration
  --phone <number>   Send test message to phone number (format: 5511999999999)
  --debug           Show detailed API information for troubleshooting
  --help, -h        Show this help message
    `);
    process.exit(0);
  }

  tester.runAllTests(options);
}

module.exports = EvolutionApiTester;
