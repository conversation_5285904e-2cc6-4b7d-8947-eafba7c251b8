#!/usr/bin/env node

const N8nApiClient = require('./n8n_api_client.js');

async function activateEssentialSubflows() {
  const client = new N8nApiClient(
    'https://n8n-n8n.w9jo16.easypanel.host', 
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
  );

  try {
    console.log('🔍 Verificando estado pós-limpeza...');
    
    const workflows = await client.getWorkflows();
    console.log(`📊 Total de workflows após limpeza: ${workflows.data.length}`);
    
    const activeWorkflows = workflows.data.filter(wf => wf.active);
    console.log(`🟢 Workflows ativos: ${activeWorkflows.length}`);
    
    // Subfluxos essenciais para ativar
    const essentialSubflows = [
      'Session_Manager_Subflow',
      'Onboarding_Orchestrator_Subflow',
      'Message_Processor_Subflow', 
      'Persistence_Memory_Subflow',
      'Normalize_Extract_Subflow',
      'User_Registry_Access_Subflow'
    ];
    
    console.log('\n🚀 Ativando subfluxos essenciais...');
    
    for (const subflowName of essentialSubflows) {
      const workflow = workflows.data.find(wf => wf.name === subflowName);
      
      if (workflow) {
        if (!workflow.active) {
          try {
            await client.activateWorkflow(workflow.id);
            console.log(`✅ Ativado: ${subflowName}`);
          } catch (error) {
            console.log(`❌ Erro ao ativar ${subflowName}: ${error.message}`);
          }
        } else {
          console.log(`🟢 Já ativo: ${subflowName}`);
        }
      } else {
        console.log(`⚠️ Não encontrado: ${subflowName}`);
      }
    }
    
    // Verificar estado final
    console.log('\n📊 ESTADO FINAL:');
    const finalWorkflows = await client.getWorkflows();
    const finalActive = finalWorkflows.data.filter(wf => wf.active);
    
    console.log(`Total de workflows: ${finalWorkflows.data.length}`);
    console.log(`Workflows ativos: ${finalActive.length}`);
    
    console.log('\n🟢 WORKFLOWS ATIVOS:');
    finalActive.forEach(wf => {
      console.log(`  - ${wf.name}`);
    });
    
  } catch (error) {
    console.error('Erro:', error.message);
  }
}

activateEssentialSubflows();
