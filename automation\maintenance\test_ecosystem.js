#!/usr/bin/env node

/**
 * WhatsApp AI Assistant Ecosystem Testing Framework
 * 
 * This script provides comprehensive testing for the deployed ecosystem,
 * including workflow validation, API connectivity, and end-to-end flow testing.
 */

const N8nApiClient = require('./n8n_api_client.js');
const path = require('path');
const fs = require('fs').promises;

// Test Configuration
const TEST_CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'http://localhost:5678',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M',
  testPhoneNumber: '5511999999999', // Test phone number
  expectedWorkflows: [
    'WhatsApp Message Router v1',
    'SuperUser Authentication v1',
    'Session Manager v1',
    'Main Orchestrator Agent v1',
    'Message Processor v1'
  ]
};

// Test scenarios for end-to-end testing
const TEST_SCENARIOS = [
  {
    name: 'Authorized User Authentication',
    description: 'Test authentication flow for authorized super user',
    testData: {
      phone: TEST_CONFIG.testPhoneNumber,
      content: 'Olá, preciso de ajuda com informações da Cruz Vermelha'
    },
    expectedOutcome: 'authentication_success'
  },
  {
    name: 'Unauthorized User Rejection',
    description: 'Test rejection of unauthorized user',
    testData: {
      phone: '5511888888888',
      content: 'Teste de acesso não autorizado'
    },
    expectedOutcome: 'authentication_failure'
  },
  {
    name: 'Red Cross Information Query',
    description: 'Test knowledge base search functionality',
    testData: {
      phone: TEST_CONFIG.testPhoneNumber,
      content: 'Quais são os princípios fundamentais da Cruz Vermelha?'
    },
    expectedOutcome: 'knowledge_search_success'
  },
  {
    name: 'Appointment Scheduling Request',
    description: 'Test appointment scheduling functionality',
    testData: {
      phone: TEST_CONFIG.testPhoneNumber,
      content: 'Gostaria de agendar uma consulta psicológica para amanhã de manhã'
    },
    expectedOutcome: 'scheduling_success'
  }
];

class EcosystemTester {
  constructor() {
    this.client = new N8nApiClient(TEST_CONFIG.n8nApiUrl, TEST_CONFIG.n8nApiKey);
    this.testResults = [];
  }

  // Test n8n API connectivity
  async testApiConnectivity() {
    console.log('🔗 Testing n8n API connectivity...');
    
    try {
      const workflows = await this.client.getWorkflows();
      return {
        test: 'API Connectivity',
        status: 'PASS',
        message: `Connected successfully. Found ${workflows.data?.length || 0} workflows.`,
        details: { workflow_count: workflows.data?.length || 0 }
      };
    } catch (error) {
      return {
        test: 'API Connectivity',
        status: 'FAIL',
        message: `Connection failed: ${error.message}`,
        details: { error: error.message }
      };
    }
  }

  // Test workflow deployment status
  async testWorkflowDeployment() {
    console.log('📋 Testing workflow deployment status...');
    
    const results = [];
    
    for (const expectedWorkflow of TEST_CONFIG.expectedWorkflows) {
      try {
        const workflow = await this.client.findWorkflowByName(expectedWorkflow);
        
        if (workflow) {
          results.push({
            workflow: expectedWorkflow,
            status: 'DEPLOYED',
            active: workflow.active,
            id: workflow.id,
            created: workflow.createdAt,
            updated: workflow.updatedAt
          });
        } else {
          results.push({
            workflow: expectedWorkflow,
            status: 'MISSING',
            active: false,
            id: null
          });
        }
      } catch (error) {
        results.push({
          workflow: expectedWorkflow,
          status: 'ERROR',
          active: false,
          id: null,
          error: error.message
        });
      }
    }
    
    const deployedCount = results.filter(r => r.status === 'DEPLOYED').length;
    const activeCount = results.filter(r => r.active).length;
    
    return {
      test: 'Workflow Deployment',
      status: deployedCount === TEST_CONFIG.expectedWorkflows.length ? 'PASS' : 'FAIL',
      message: `${deployedCount}/${TEST_CONFIG.expectedWorkflows.length} workflows deployed, ${activeCount} active`,
      details: { workflows: results, deployed_count: deployedCount, active_count: activeCount }
    };
  }

  // Test workflow dependencies
  async testWorkflowDependencies() {
    console.log('🔗 Testing workflow dependencies...');
    
    const dependencies = [
      { workflow: 'WhatsApp Message Router v1', depends_on: ['SuperUser Authentication v1'] },
      { workflow: 'Session Manager v1', depends_on: ['SuperUser Authentication v1'] },
      { workflow: 'Main Orchestrator Agent v1', depends_on: ['Session Manager v1'] },
      { workflow: 'Message Processor v1', depends_on: ['Main Orchestrator Agent v1'] }
    ];
    
    const results = [];
    
    for (const dep of dependencies) {
      const workflow = await this.client.findWorkflowByName(dep.workflow);
      const dependencyChecks = [];
      
      for (const dependency of dep.depends_on) {
        const dependencyWorkflow = await this.client.findWorkflowByName(dependency);
        dependencyChecks.push({
          dependency: dependency,
          exists: !!dependencyWorkflow,
          active: dependencyWorkflow?.active || false
        });
      }
      
      const allDependenciesOk = dependencyChecks.every(check => check.exists && check.active);
      
      results.push({
        workflow: dep.workflow,
        exists: !!workflow,
        dependencies_satisfied: allDependenciesOk,
        dependency_details: dependencyChecks
      });
    }
    
    const allDependenciesOk = results.every(r => r.exists && r.dependencies_satisfied);
    
    return {
      test: 'Workflow Dependencies',
      status: allDependenciesOk ? 'PASS' : 'FAIL',
      message: allDependenciesOk ? 'All dependencies satisfied' : 'Some dependencies missing or inactive',
      details: { dependency_checks: results }
    };
  }

  // Test webhook endpoints
  async testWebhookEndpoints() {
    console.log('🌐 Testing webhook endpoints...');
    
    try {
      const routerWorkflow = await this.client.findWorkflowByName('WhatsApp Message Router v1');
      
      if (!routerWorkflow) {
        return {
          test: 'Webhook Endpoints',
          status: 'FAIL',
          message: 'WhatsApp Message Router workflow not found',
          details: {}
        };
      }
      
      // Check if webhook node exists in the workflow
      const hasWebhookNode = routerWorkflow.nodes?.some(node => 
        node.type === 'n8n-nodes-base.webhook' && 
        node.parameters?.path === 'whatsapp-webhook'
      );
      
      return {
        test: 'Webhook Endpoints',
        status: hasWebhookNode ? 'PASS' : 'FAIL',
        message: hasWebhookNode ? 'Webhook endpoint configured correctly' : 'Webhook endpoint not found',
        details: { 
          workflow_id: routerWorkflow.id,
          webhook_configured: hasWebhookNode,
          expected_path: 'whatsapp-webhook'
        }
      };
      
    } catch (error) {
      return {
        test: 'Webhook Endpoints',
        status: 'ERROR',
        message: `Error testing webhook endpoints: ${error.message}`,
        details: { error: error.message }
      };
    }
  }

  // Simulate workflow execution (basic validation)
  async simulateWorkflowExecution(scenario) {
    console.log(`🎭 Simulating: ${scenario.name}`);
    
    try {
      // This is a simulation - in a real test, you would trigger the webhook
      // For now, we'll just validate the workflow structure
      
      const routerWorkflow = await this.client.findWorkflowByName('WhatsApp Message Router v1');
      
      if (!routerWorkflow || !routerWorkflow.active) {
        return {
          scenario: scenario.name,
          status: 'FAIL',
          message: 'Router workflow not active',
          details: { workflow_active: false }
        };
      }
      
      // Check if all required workflows in the chain are active
      const requiredWorkflows = [
        'SuperUser Authentication v1',
        'Session Manager v1',
        'Main Orchestrator Agent v1',
        'Message Processor v1'
      ];
      
      const workflowStatuses = [];
      for (const workflowName of requiredWorkflows) {
        const workflow = await this.client.findWorkflowByName(workflowName);
        workflowStatuses.push({
          name: workflowName,
          exists: !!workflow,
          active: workflow?.active || false
        });
      }
      
      const allWorkflowsReady = workflowStatuses.every(w => w.exists && w.active);
      
      return {
        scenario: scenario.name,
        status: allWorkflowsReady ? 'PASS' : 'FAIL',
        message: allWorkflowsReady ? 'All workflows in chain are ready' : 'Some workflows in chain are not ready',
        details: { 
          test_data: scenario.testData,
          expected_outcome: scenario.expectedOutcome,
          workflow_chain_status: workflowStatuses
        }
      };
      
    } catch (error) {
      return {
        scenario: scenario.name,
        status: 'ERROR',
        message: `Simulation error: ${error.message}`,
        details: { error: error.message }
      };
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🧪 WhatsApp AI Assistant Ecosystem Test Suite\n');
    console.log('🎯 Target n8n Instance:', TEST_CONFIG.n8nApiUrl);
    console.log('📱 Test Phone Number:', TEST_CONFIG.testPhoneNumber);
    console.log('');
    
    const testResults = [];
    
    // Core infrastructure tests
    testResults.push(await this.testApiConnectivity());
    testResults.push(await this.testWorkflowDeployment());
    testResults.push(await this.testWorkflowDependencies());
    testResults.push(await this.testWebhookEndpoints());
    
    // Scenario-based tests
    console.log('\n🎭 Running scenario-based tests...');
    for (const scenario of TEST_SCENARIOS) {
      testResults.push(await this.simulateWorkflowExecution(scenario));
    }
    
    // Generate test summary
    const summary = this.generateTestSummary(testResults);
    
    // Save test report
    await this.saveTestReport(testResults, summary);
    
    // Display results
    this.displayTestResults(testResults, summary);
    
    return { results: testResults, summary };
  }

  // Generate test summary
  generateTestSummary(results) {
    const total = results.length;
    const passed = results.filter(r => r.status === 'PASS').length;
    const failed = results.filter(r => r.status === 'FAIL').length;
    const errors = results.filter(r => r.status === 'ERROR').length;
    
    return {
      total_tests: total,
      passed: passed,
      failed: failed,
      errors: errors,
      success_rate: ((passed / total) * 100).toFixed(1) + '%',
      test_timestamp: new Date().toISOString()
    };
  }

  // Save test report
  async saveTestReport(results, summary) {
    const reportPath = path.join(__dirname, `test_report_${Date.now()}.json`);
    
    const report = {
      test_info: {
        timestamp: new Date().toISOString(),
        n8n_url: TEST_CONFIG.n8nApiUrl,
        test_suite_version: '1.0.0'
      },
      summary: summary,
      test_results: results
    };
    
    try {
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n📄 Test report saved: ${reportPath}`);
    } catch (error) {
      console.warn('⚠️ Failed to save test report:', error.message);
    }
  }

  // Display test results
  displayTestResults(results, summary) {
    console.log('\n📊 Test Results Summary');
    console.log('=' .repeat(50));
    
    results.forEach(result => {
      const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
      console.log(`${statusIcon} ${result.test || result.scenario}: ${result.message}`);
    });
    
    console.log('\n' + '=' .repeat(50));
    console.log(`📈 Overall Results: ${summary.passed}/${summary.total_tests} tests passed (${summary.success_rate})`);
    console.log(`✅ Passed: ${summary.passed}`);
    console.log(`❌ Failed: ${summary.failed}`);
    console.log(`⚠️ Errors: ${summary.errors}`);
    console.log('=' .repeat(50));
    
    if (summary.failed > 0 || summary.errors > 0) {
      console.log('\n⚠️ Some tests failed. Please review the test report for details.');
      process.exit(1);
    } else {
      console.log('\n🎉 All tests passed! Your ecosystem is ready for production.');
      process.exit(0);
    }
  }
}

// CLI execution
if (require.main === module) {
  const tester = new EcosystemTester();
  tester.runAllTests();
}

module.exports = EcosystemTester;
