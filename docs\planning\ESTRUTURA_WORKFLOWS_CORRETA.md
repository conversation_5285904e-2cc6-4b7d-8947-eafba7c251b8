# 🏗️ ESTRUTURA CORRETA DOS WORKFLOWS - AI COMTXAE

**Data**: 18/09/2025  
**Objetivo**: Definir estrutura correta para 8 workflows especializados  
**Padrão**: Unified_User_Pipeline com granularidade específica  

---

## 📋 WORKFLOWS IDENTIFICADOS (8 TOTAL)

### **NÍVEL 1 - WORKFLOW PRINCIPAL (1)**
1. **`Unified_User_Pipeline`** - Webhook único, roteamento principal

### **NÍVEL 2 - SUBFLUXOS ESPECIALIZADOS (6)**
2. **`Normalize_Extract_Subflow`** - Normalização e extração de dados
3. **`User_Registry_Access_Subflow`** - Acesso e gestão de usuários
4. **`Session_Manager_Subflow`** - Gerenciamento de sessões
5. **`Onboarding_Orchestrator_Subflow`** - Orquestração do onboarding
6. **`Message_Processor_Subflow`** - Processamento de mensagens
7. **`Persistence_Memory_Subflow`** - Persistência e memória

### **NÍVEL 3 - WORKFLOW ADICIONAL (1)**
8. **`Database_Schema_Generator`** - Geração automática de schema

---

## 🚨 PROBLEMAS IDENTIFICADOS NOS WORKFLOWS ATUAIS

### **1. Nós Inadequados**
- **Problema**: Workflows usam nós `Supabase` que esperam `Query`
- **Realidade**: Nós Supabase executam apenas operações específicas (create, get, delete)
- **Solução**: Usar nó `Postgres` para queries SQL customizadas

### **2. Conexões Incorretas**
- **Problema**: Nós "Execute Sub-Workflow" substituídos mas conexões não definidas
- **Impacto**: Fluxos inoperantes sem comunicação entre nós
- **Solução**: Definir conexões específicas para cada workflow

### **3. Falta de Granularidade**
- **Problema**: Prompts genéricos para workflows complexos
- **Impacto**: Implementações superficiais e não funcionais
- **Solução**: Um agente específico para cada workflow

---

## 🎯 ESTRATÉGIA DE CORREÇÃO

### **PRINCÍPIO**: Um Agente por Workflow
Cada workflow deve ter um agente remoto especializado para:
- Analisar requisitos específicos do workflow
- Escolher nós apropriados (Postgres vs Supabase)
- Definir conexões corretas entre nós
- Implementar lógica específica
- Testar funcionamento individual

### **ORDEM DE EXECUÇÃO RECOMENDADA**:
1. **Agent 1** - Unified_User_Pipeline (principal)
2. **Agent 2** - Normalize_Extract_Subflow
3. **Agent 3** - User_Registry_Access_Subflow
4. **Agent 4** - Session_Manager_Subflow
5. **Agent 5** - Onboarding_Orchestrator_Subflow
6. **Agent 6** - Message_Processor_Subflow
7. **Agent 7** - Persistence_Memory_Subflow
8. **Agent 8** - Database_Schema_Generator

---

## 🔧 ESPECIFICAÇÕES TÉCNICAS POR WORKFLOW

### **1. Unified_User_Pipeline**
- **Função**: Webhook único, roteamento por access_level
- **Nós Principais**: Webhook, Switch (roteamento), Execute Workflow (6x)
- **Conexões**: WhatsApp → Roteamento → Subfluxos
- **Dados**: CPF, phone, access_level, onboarding_status

### **2. Normalize_Extract_Subflow**
- **Função**: Normalizar dados de entrada, extrair informações
- **Nós Principais**: Code (Python), Set, Merge
- **Conexões**: Entrada → Normalização → Extração → Saída
- **Dados**: Mensagem bruta → Dados estruturados

### **3. User_Registry_Access_Subflow**
- **Função**: Buscar/criar usuário, validar CPF
- **Nós Principais**: Postgres (query), Supabase (create/update)
- **Conexões**: CPF → Busca → Validação → Criação/Atualização
- **Tabelas**: `users`, `super_users`

### **4. Session_Manager_Subflow**
- **Função**: Gerenciar sessões de chat, histórico
- **Nós Principais**: Postgres (query), Supabase (create/update)
- **Conexões**: User_ID → Sessão → Histórico → Persistência
- **Tabelas**: `chat_sessions`, `chat_messages`

### **5. Onboarding_Orchestrator_Subflow**
- **Função**: Orquestrar processo de onboarding gamificado
- **Nós Principais**: Switch (nível), Code (lógica), Supabase (update)
- **Conexões**: Access_Level → Lógica → Atualização → Resposta
- **Tabelas**: `users` (onboarding_progress)

### **6. Message_Processor_Subflow**
- **Função**: Processar mensagens, IA, respostas
- **Nós Principais**: OpenAI, Code (processamento), HTTP (Evolution API)
- **Conexões**: Mensagem → IA → Processamento → Resposta
- **APIs**: OpenAI, Evolution API

### **7. Persistence_Memory_Subflow**
- **Função**: Persistir memória, contexto, histórico
- **Nós Principais**: Postgres (complex queries), Supabase (simple ops)
- **Conexões**: Dados → Processamento → Persistência → Confirmação
- **Tabelas**: `chat_memory_history`, `user_qa_context`

### **8. Database_Schema_Generator**
- **Função**: Gerar/atualizar schema automaticamente
- **Nós Principais**: Postgres (DDL), Code (validação)
- **Conexões**: Trigger → Análise → Geração → Aplicação
- **Escopo**: Todas as 20 tabelas do Supabase

---

## 📊 MÉTRICAS DE QUALIDADE

### **Para Cada Workflow:**
- ✅ **Nós Apropriados**: Postgres para queries, Supabase para CRUD
- ✅ **Conexões Funcionais**: Todos os nós conectados corretamente
- ✅ **Lógica Específica**: Implementação detalhada para o propósito
- ✅ **Teste Individual**: Cada workflow deve funcionar isoladamente
- ✅ **Integração**: Comunicação correta com outros workflows

### **Para o Sistema:**
- ✅ **Webhook Único**: Apenas Unified_User_Pipeline
- ✅ **Hierarquia Clara**: Principal → Subfluxos → Utilitários
- ✅ **Dados Consistentes**: Fluxo de dados entre workflows
- ✅ **Performance**: Tempo de resposta < 3 segundos
- ✅ **Confiabilidade**: Taxa de sucesso > 95%

---

## 🎯 PRÓXIMOS PASSOS

1. **Atualizar prompts** dos 8 agentes com especificações detalhadas
2. **Incluir Context 7 MCP** para nós atualizados do n8n
3. **Incluir Supabase MCP** para schema das 20 tabelas
4. **Executar agentes sequencialmente** na ordem definida
5. **Testar cada workflow** individualmente
6. **Validar integração** completa do sistema

**Status**: Estrutura definida, pronta para implementação dos prompts corrigidos
