#!/usr/bin/env node

/**
 * OpenAI Credentials Configuration Script for n8n
 * 
 * This script configures OpenAI API credentials in n8n using the Management API.
 * It creates the necessary credential entries that workflows can reference.
 * 
 * Usage: node automation/configure_openai_credentials.js
 */

require('dotenv').config();
const N8nApiClient = require('./n8n_api_client.js');

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'http://localhost:5678',
  n8nApiKey: process.env.N8N_API_KEY,
  openaiApiKey: process.env.OPENAI_API_KEY,
  openaiModel: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
  openaiMaxTokens: process.env.OPENAI_MAX_TOKENS || 1000,
  openaiTemperature: process.env.OPENAI_TEMPERATURE || 0.7
};

class OpenAICredentialConfigurator {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
  }

  // Main configuration process
  async configureCredentials() {
    console.log('🔑 Configuring OpenAI Credentials in n8n\n');
    console.log('🎯 Target n8n Instance:', CONFIG.n8nApiUrl);
    console.log('🤖 OpenAI Model:', CONFIG.openaiModel);
    console.log('');

    try {
      // Step 1: Check if OpenAI credentials already exist
      const existingCredentials = await this.checkExistingCredentials();
      
      // Step 2: Create or update OpenAI credentials
      if (existingCredentials.length > 0) {
        console.log('📝 Updating existing OpenAI credentials...');
        await this.updateCredentials(existingCredentials[0]);
      } else {
        console.log('🆕 Creating new OpenAI credentials...');
        await this.createCredentials();
      }

      // Step 3: Verify credentials
      await this.verifyCredentials();

      console.log('\n✅ OpenAI credentials configured successfully!');
      return true;

    } catch (error) {
      console.error('❌ Credential configuration failed:', error.message);
      console.error('Stack trace:', error.stack);
      return false;
    }
  }

  // Check for existing OpenAI credentials
  async checkExistingCredentials() {
    try {
      console.log('🔍 Checking for existing OpenAI credentials...');
      
      // Get all credentials
      const credentials = await this.client.getCredentials();
      
      // Filter for OpenAI credentials
      const openaiCredentials = credentials.filter(cred => 
        cred.type === 'openAiApi' || 
        cred.name.toLowerCase().includes('openai') ||
        cred.type === 'httpHeaderAuth' && cred.name.toLowerCase().includes('openai')
      );

      console.log(`  Found ${openaiCredentials.length} existing OpenAI credential(s)`);
      return openaiCredentials;

    } catch (error) {
      console.warn('  ⚠️  Could not check existing credentials:', error.message);
      return [];
    }
  }

  // Create new OpenAI credentials
  async createCredentials() {
    const credentialData = {
      name: 'OpenAI API',
      type: 'httpHeaderAuth',
      data: {
        name: 'Authorization',
        value: `Bearer ${CONFIG.openaiApiKey}`
      }
    };

    try {
      const result = await this.client.createCredential(credentialData);
      console.log('  ✅ OpenAI credentials created successfully');
      console.log('  📋 Credential ID:', result.id);
      return result;

    } catch (error) {
      // If the specific credential type doesn't work, try a generic approach
      console.warn('  ⚠️  Standard credential creation failed, trying generic approach...');
      
      const genericCredentialData = {
        name: 'OpenAI API Key',
        type: 'httpHeaderAuth',
        data: {
          name: 'Authorization',
          value: `Bearer ${CONFIG.openaiApiKey}`
        }
      };

      try {
        const result = await this.client.createCredential(genericCredentialData);
        console.log('  ✅ Generic OpenAI credentials created successfully');
        return result;
      } catch (genericError) {
        throw new Error(`Failed to create credentials: ${error.message} | Generic attempt: ${genericError.message}`);
      }
    }
  }

  // Update existing credentials
  async updateCredentials(existingCredential) {
    const updatedData = {
      ...existingCredential,
      data: {
        name: 'Authorization',
        value: `Bearer ${CONFIG.openaiApiKey}`
      }
    };

    try {
      const result = await this.client.updateCredential(existingCredential.id, updatedData);
      console.log('  ✅ OpenAI credentials updated successfully');
      console.log('  📋 Credential ID:', existingCredential.id);
      return result;

    } catch (error) {
      throw new Error(`Failed to update credentials: ${error.message}`);
    }
  }

  // Verify credentials by testing API connection
  async verifyCredentials() {
    console.log('🧪 Verifying OpenAI API connection...');

    try {
      // Test OpenAI API directly
      const testResponse = await this.testOpenAIConnection();
      
      if (testResponse.success) {
        console.log('  ✅ OpenAI API connection verified');
        console.log('  📊 Available models:', testResponse.models?.slice(0, 3).join(', ') + '...');
      } else {
        console.warn('  ⚠️  OpenAI API connection test failed:', testResponse.error);
      }

    } catch (error) {
      console.warn('  ⚠️  Could not verify OpenAI connection:', error.message);
    }
  }

  // Test OpenAI API connection
  async testOpenAIConnection() {
    try {
      const fetch = require('node-fetch');
      
      // Test with a simple models list request
      const response = await fetch('https://api.openai.com/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${CONFIG.openaiApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          models: data.data?.map(model => model.id) || []
        };
      } else {
        const errorData = await response.text();
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorData}`
        };
      }

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Generate configuration instructions
  generateInstructions() {
    console.log('\n📋 Manual Configuration Instructions:');
    console.log('');
    console.log('If automatic configuration failed, follow these steps:');
    console.log('');
    console.log('1. Open your n8n interface');
    console.log('2. Go to Settings > Credentials');
    console.log('3. Click "Add Credential"');
    console.log('4. Search for "HTTP Header Auth" or "OpenAI"');
    console.log('5. Configure with these values:');
    console.log('   - Name: OpenAI API');
    console.log('   - Header Name: Authorization');
    console.log(`   - Header Value: Bearer ${CONFIG.openaiApiKey.substring(0, 20)}...`);
    console.log('6. Test the credential');
    console.log('7. Save the credential');
    console.log('');
    console.log('Then update your workflow nodes to use this credential.');
  }
}

// Main execution
async function main() {
  if (!CONFIG.n8nApiKey) {
    console.error('❌ N8N_API_KEY environment variable is required');
    process.exit(1);
  }

  if (!CONFIG.openaiApiKey) {
    console.error('❌ OPENAI_API_KEY environment variable is required');
    process.exit(1);
  }

  const configurator = new OpenAICredentialConfigurator();
  const success = await configurator.configureCredentials();
  
  if (!success) {
    configurator.generateInstructions();
  }
  
  process.exit(success ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = OpenAICredentialConfigurator;
