{"environment": "production", "version": "1.0.0", "last_updated": "2025-08-27T00:00:00.000Z", "n8n": {"base_url": "https://n8n-n8n.w9jo16.easypanel.host", "webhook_url": "https://n8n-n8n.w9jo16.easypanel.host/webhook", "execution_mode": "main", "log_level": "warn", "max_execution_timeout": 300, "webhook_timeout": 30, "default_timezone": "America/Sao_Paulo"}, "workflows": {"whatsapp_message_router": {"id": "WhatsApp_Message_Router_v1", "active": true, "webhook_path": "whatsapp-webhook", "timeout": 30}, "superuser_authentication": {"id": "SuperUser_Authentication_v1", "active": false, "timeout": 10}, "session_manager": {"id": "Session_Manager_v1", "active": false, "timeout": 15}, "main_orchestrator": {"id": "Main_Orchestrator_Agent_v2", "active": false, "timeout": 60}, "message_processor": {"id": "Message_Processor_v1", "active": false, "timeout": 20}}, "apis": {"evolution_api": {"base_url": "https://evolution-evolution-api.w9jo16.easypanel.host", "instance": "Lucas_4569", "timeout": 30, "retry_attempts": 3, "retry_delay": 1000, "endpoints": {"send_text": "/message/sendText/{instance}", "send_media": "/message/sendMedia/{instance}", "instance_status": "/instance/fetchInstances", "webhook_config": "/webhook/set/{instance}"}}, "openai": {"base_url": "https://api.openai.com/v1", "model": "gpt-3.5-turbo", "max_tokens": 1000, "temperature": 0.7, "timeout": 60, "retry_attempts": 2, "retry_delay": 2000, "endpoints": {"chat_completions": "/chat/completions", "models": "/models"}}, "supabase": {"base_url": "https://qvkstxeayyzrntywtf.supabase.co", "rest_endpoint": "/rest/v1", "timeout": 30, "retry_attempts": 3, "retry_delay": 1000, "headers": {"Content-Type": "application/json", "Prefer": "return=representation"}}}, "database": {"tables": {"super_users": {"name": "super_users", "primary_key": "id", "indexes": ["phone", "active", "(phone, active)"]}, "chat_sessions": {"name": "chat_sessions", "primary_key": "id", "indexes": ["user_id", "channel_type", "channel_identifier", "active"]}, "chat_messages": {"name": "chat_messages", "primary_key": "id", "indexes": ["session_id", "user_id", "created_at", "delivery_status", "message_type"]}, "documents": {"name": "documents", "primary_key": "id", "indexes": ["document_type", "embedding"]}, "appointments": {"name": "appointments", "primary_key": "id", "indexes": ["user_id", "date", "status", "channel_type"]}, "authentication_logs": {"name": "authentication_logs", "primary_key": "id", "indexes": ["phone", "timestamp", "status", "user_id"]}}}, "security": {"authentication": {"required": true, "method": "database_lookup", "session_timeout": 3600, "max_login_attempts": 5, "lockout_duration": 900}, "rate_limiting": {"enabled": true, "max_requests_per_minute": 60, "max_requests_per_hour": 1000, "burst_limit": 10}, "logging": {"log_all_requests": true, "log_failed_auth": true, "log_sensitive_data": false, "retention_days": 90}}, "features": {"debug_logging": false, "test_mode": false, "skip_authentication": false, "mock_apis": false, "detailed_error_messages": false, "performance_monitoring": true, "health_checks": true}, "monitoring": {"health_check_interval": 300, "metrics_collection": true, "alert_thresholds": {"response_time_ms": 5000, "error_rate_percent": 5, "queue_length": 100}, "notifications": {"webhook_failures": true, "authentication_failures": true, "api_errors": true, "performance_degradation": true}}, "performance": {"max_concurrent_executions": 50, "queue_mode": "main", "execution_timeout": 300, "memory_limit_mb": 512, "cpu_limit_percent": 80}, "backup": {"enabled": true, "frequency": "daily", "retention_days": 30, "include_credentials": false, "backup_location": "automated_backups/"}, "deployment": {"strategy": "blue_green", "health_check_url": "/health", "readiness_check_url": "/ready", "graceful_shutdown_timeout": 30, "pre_deployment_checks": ["database_connectivity", "api_connectivity", "credential_validation"], "post_deployment_checks": ["workflow_activation", "webhook_connectivity", "end_to_end_test"]}}