{"timestamp": "2025-09-19T13:57:06.541459", "total_conflicts": 66, "conflicts_by_type": {"schema_references": 0, "workflow_names": 6, "architecture_diagrams": 0, "api_endpoints": 1, "database_queries": 3, "table_counts": 44, "user_model_references": 12}, "detailed_conflicts": {"schema_references": [], "workflow_names": [{"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "old_name": "Session Manager v1", "issue": "Workflow name obsoleto", "expected": "Unified_User_Pipeline architecture"}, {"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "old_name": "Message Processor v1", "issue": "Workflow name obsoleto", "expected": "Unified_User_Pipeline architecture"}, {"file": "docs/architecture/DOCUMENTATION_AUDIT_REPORT.md", "old_name": "SuperUser Authentication", "issue": "Workflow name obsoleto", "expected": "Unified_User_Pipeline architecture"}, {"file": "docs/architecture/DOCUMENTATION_AUDIT_REPORT.md", "old_name": "Session Manager v1", "issue": "Workflow name obsoleto", "expected": "Unified_User_Pipeline architecture"}, {"file": "docs/architecture/DOCUMENTATION_AUDIT_REPORT.md", "old_name": "Main Orchestrator Agent", "issue": "Workflow name obsoleto", "expected": "Unified_User_Pipeline architecture"}, {"file": "docs/architecture/DOCUMENTATION_AUDIT_REPORT.md", "old_name": "Message Processor v1", "issue": "Workflow name obsoleto", "expected": "Unified_User_Pipeline architecture"}], "architecture_diagrams": [], "api_endpoints": [{"file": "SCHEMA_MIGRATION_TASKS.md", "issue": "Endpoint de API obsoleto", "expected": "/users/{cpf} endpoints"}], "database_queries": [{"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "issue": "Query SQL usando estrutura obsoleta", "expected": "users.cpf como PK"}, {"file": "docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md", "issue": "Query SQL usando estrutura obsoleta", "expected": "users.cpf como PK"}, {"file": "docs/architecture/SCHEMA_MIGRATION_COMPLETE_REPORT.md", "issue": "Query SQL usando estrutura obsoleta", "expected": "users.cpf como PK"}], "table_counts": [{"file": "INTEGRATION_VALIDATION_REPORT.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "INTEGRATION_VALIDATION_REPORT.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "INTEGRATION_VALIDATION_REPORT.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "README.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "README.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "README_PROJETO_LEAN.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "remote_agents.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "remote_agents.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "remote_agents.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "remote_agents.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "remote_agents_unificados.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "remote_agents_workflows_especificos.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/PLANO_IMPLEMENTACAO_AGENTES.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/PLANO_IMPLEMENTACAO_AGENTES.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/PLANO_IMPLEMENTACAO_AGENTES.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/PLANO_IMPLEMENTACAO_AGENTES.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "documented": 12, "real": 20, "issue": "Contagem incorreta: 12 vs 20"}, {"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "documented": 7, "real": 20, "issue": "Contagem incorreta: 7 vs 20"}, {"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "documented": 7, "real": 20, "issue": "Contagem incorreta: 7 vs 20"}, {"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "documented": 8, "real": 20, "issue": "Contagem incorreta: 8 vs 20"}, {"file": "docs/architecture/DATABASE_SCHEMA_OPTIMIZATION_REPORT.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/DATABASE_SCHEMA_OPTIMIZATION_REPORT.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/DATABASE_SCHEMA_OPTIMIZATION_REPORT.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/DATABASE_SECURITY_RLS_REPORT.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/DATABASE_SECURITY_RLS_REPORT.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/DATABASE_SECURITY_RLS_REPORT.md", "documented": 8, "real": 20, "issue": "Contagem incorreta: 8 vs 20"}, {"file": "docs/architecture/DATABASE_SECURITY_RLS_REPORT.md", "documented": 8, "real": 20, "issue": "Contagem incorreta: 8 vs 20"}, {"file": "docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md", "documented": 9, "real": 20, "issue": "Contagem incorreta: 9 vs 20"}, {"file": "docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/SCHEMA_MIGRATION_COMPLETE_REPORT.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/architecture/SCHEMA_MIGRATION_COMPLETE_REPORT.md", "documented": 8, "real": 20, "issue": "Contagem incorreta: 8 vs 20"}, {"file": "docs/architecture/DOCUMENTATION_AUDIT_REPORT.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/planning/ESTRUTURA_WORKFLOWS_CORRETA.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/planning/ESTRUTURA_WORKFLOWS_CORRETA.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/planning/QUESTIONARIO_LACUNAS_INFORMACAO.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/planning/SPRINT_ATUAL.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/planning/SPRINT_ATUAL.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/planning/SPRINT_ATUAL.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}, {"file": "docs/planning/SPRINT_ATUAL.md", "documented": 16, "real": 20, "issue": "Contagem incorreta: 16 vs 20"}], "user_model_references": [{"file": "INTEGRATION_VALIDATION_REPORT.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "README_PROJETO_LEAN.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "SCHEMA_MIGRATION_TASKS.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "remote_agents.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "remote_agents_workflows_especificos.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "docs/architecture/ARCHITECTURE_VALIDATION.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "docs/architecture/DATABASE_SCHEMA_OPTIMIZATION_REPORT.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "docs/architecture/SCHEMA_MIGRATION_COMPLETE_REPORT.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "docs/architecture/DOCUMENTATION_AUDIT_REPORT.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "docs/planning/ESTRUTURA_WORKFLOWS_CORRETA.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}, {"file": "docs/planning/QUESTIONARIO_LACUNAS_INFORMACAO.md", "issue": "Referência ao modelo superuser/non-superuser obsoleto", "expected": "CPF unificado"}]}, "recommendations": [{"priority": "HIGH", "action": "Substituir todas as referências superuser/non-superuser por CPF unificado", "files_affected": 12}, {"priority": "HIGH", "action": "Atual<PERSON>r contagem de tabelas para 20", "files_affected": 44}, {"priority": "MEDIUM", "action": "Atualizar nomes de workflows para arquitetura Unified_User_Pipeline", "files_affected": 6}]}