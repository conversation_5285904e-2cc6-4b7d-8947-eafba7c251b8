# 🗄️ SCHEMA ATUAL DO SUPABASE - AI COMTXAE

**Data**: 19/09/2025  
**Projeto**: assistente_v0.1 (ID: qvkstxeayyzrntywifdi)  
**Região**: sa-east-1 (Brasil)  
**Status**: ✅ **20 TABELAS IMPLEMENTADAS**

---

## 📊 **TABELAS IMPLEMENTADAS (20 TOTAL)**

### **1. USUÁRIOS E AUTENTICAÇÃO**
- `users` - <PERSON>bela principal de usuários (CPF como PK)
- `authentication_logs` - Logs de autenticação
- `user_contact_methods` - Métodos de contato dos usuários
- `user_context` - Contexto dos usuários
- `user_development_matrix` - <PERSON>riz de desenvolvimento
- `user_organization_roles` - Papéis dos usuários nas organizações
- `user_qa_context` - Contexto de perguntas e respostas

### **2. CHAT E MENSAGENS**
- `chat_sessions` - Sessões de chat
- `chat_messages` - Mensagens do chat
- `chat_memory_history` - Histórico de memória do chat

### **3. ORGANIZAÇÕES E LOCALIZAÇÕES**
- `organizations` - Organizações/comunidades
- `locations` - Localizações geográficas
- `organization_locations` - Relação organizações-localizações

### **4. AVALIAÇÕES E NECESSIDADES**
- `community_assessments` - Avaliações comunitárias
- `community_needs_assessments` - Avaliações de necessidades
- `community_needs_items` - Itens de necessidades comunitárias

### **5. PROCESSOS E TAREFAS**
- `processes` - Processos do sistema
- `tasks` - Tarefas
- `appointments` - Agendamentos

### **6. DOCUMENTOS**
- `documents` - Documentos do sistema

---

## 🔑 **ESTRUTURA PRINCIPAL**

### **IDENTIFICADOR ÚNICO: CPF**
- **Tabela principal**: `users` (CPF como Primary Key)
- **Sistema híbrido**: access_level global (1-7) + roles específicos
- **Múltiplos phones**: Permitidos por CPF via `user_contact_methods`

### **SISTEMA DE NÍVEIS DE ACESSO**
```
1. Iniciante - Primeiro contato
2. Explorador - Explorando necessidades  
3. Participante - Participação ativa
4. Colaborador - Colaboração comunitária
5. Líder - Liderança local
6. Especialista - Conhecimento avançado
7. Mentor - Orientação de outros
```

### **FLUXO DE DADOS**
```
WhatsApp → Unified_User_Pipeline → CPF Lookup → Access Level → Subfluxos
```

---

## 📈 **MÉTRICAS ATUAIS**
- **Usuários**: Sistema unificado baseado em CPF
- **Sessões**: 48+ sessões de chat migradas
- **Mensagens**: 110+ mensagens processadas
- **Organizações**: Múltiplas comunidades cadastradas
- **Uptime**: 99.5%+ (Supabase ACTIVE_HEALTHY)

---

## 🎯 **PRÓXIMOS PASSOS**

### **WORKFLOWS A IMPLEMENTAR**
1. **Unified_User_Pipeline** - Webhook único, roteamento por access_level
2. **Normalize_Extract_Subflow** - Normalização de dados
3. **User_Registry_Access_Subflow** - Gestão de usuários
4. **Session_Manager_Subflow** - Gestão de sessões
5. **Onboarding_Orchestrator_Subflow** - Onboarding gamificado
6. **Message_Processor_Subflow** - Processamento com IA
7. **Persistence_Memory_Subflow** - Persistência de memória

### **VALIDAÇÕES NECESSÁRIAS**
- ✅ Schema com 20 tabelas implementado
- ⏳ Workflows funcionais conectados
- ⏳ Sistema de roteamento por CPF
- ⏳ Integração WhatsApp → n8n → Supabase

---

**Status**: Pronto para implementação dos workflows pelos agentes remotos
