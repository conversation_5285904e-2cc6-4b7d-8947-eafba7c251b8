# =============================================================================
# WhatsApp AI Assistant - Git Ignore Configuration
# =============================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Temporary files
temp/
.tmp/
*.tmp
*.temp

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# n8n specific files
n8n-data/
.n8n/

# Database files
*.db
*.sqlite
*.sqlite3

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Build outputs
dist/
build/
out/
node_modules/

# Cache directories
.cache/
.parcel-cache/

# Backup files
*.backup
*.bak
*.old
.js

# Test outputs
test-results/
coverage/

# Deployment artifacts
deployment_report_*.json
test_report_*.json

# Local development
.local/
local/
scripts/
docs_of_previous_attemps/

# Archive and backup directories (keep structure but ignore content)
workflows/archive/*/
!workflows/archive/.gitkeep

# Temporary export/import directories
temp/exports/
temp/imports/
temp/backups/

# Keep important directories but ignore their contents
!workflows/
!workflows/active/
!docs/
!scripts/
!config/

# Ignore large workflow backup files but keep active ones
workflows/backlog_trash/
workflows/old_versions/

# Keep README files in empty directories
!**/README.md
!**/.gitkeep
