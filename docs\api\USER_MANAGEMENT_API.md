# 🔌 USER MANAGEMENT API - AI COMTXAE

**Data**: 19/09/2025  
**Versão**: 2.0 (CPF-Based)  
**Status**: ✅ **IMPLEMENTADO EM PRODUÇÃO**  
**Base URL**: `https://qvkstxeayyzrntywifdi.supabase.co/rest/v1`

---

## 🎯 **VISÃO GERAL**

API completa para gerenciamento de usuários baseada em **CPF como identificador único**, com suporte a múltiplos papéis organizacionais e sistema gamificado de 7 níveis.

### **🔑 Características**
- **Autenticação**: Supabase Auth + RLS
- **Identificação**: CPF brasileiro (11 dígitos)
- **Multi-Role**: Múltiplos papéis por usuário
- **Gamificação**: Sistema de progressão 1-7
- **Geolocalização**: Suporte a hierarquia territorial

---

## 🔐 **AUTENTICAÇÃO**

### **Headers Obrigatórios**
```http
Authorization: Bearer <supabase_anon_key>
apikey: <supabase_anon_key>
Content-Type: application/json
Prefer: return=representation
```

### **Contexto RLS**
```sql
-- Definir contexto do usuário atual
SET app.current_user_cpf = '12345678901';
```

---

## 👤 **ENDPOINTS DE USUÁRIOS**

### **1. Criar Usuário**
```http
POST /users
```

**Body:**
```json
{
  "cpf": "12345678901",
  "phone": "5521981454569",
  "name": "Lucas Lima",
  "email": "<EMAIL>",
  "access_level": 1,
  "geolocation": {
    "municipio": "Rio de Janeiro",
    "bairro": "Gávea",
    "comunidade": "Rocinha"
  },
  "onboarding_progress": {
    "level": 1,
    "current_step": "welcome",
    "completed_steps": [],
    "gamification_score": 0
  }
}
```

**Response:**
```json
{
  "cpf": "12345678901",
  "phone": "5521981454569",
  "name": "Lucas Lima",
  "access_level": 1,
  "active": true,
  "created_at": "2025-09-19T10:00:00Z"
}
```

### **2. Buscar Usuário por CPF**
```http
GET /users?cpf=eq.12345678901
```

**Response:**
```json
[
  {
    "cpf": "12345678901",
    "phone": "5521981454569",
    "name": "Lucas Lima",
    "email": "<EMAIL>",
    "access_level": 7,
    "geolocation": {...},
    "onboarding_progress": {...},
    "active": true,
    "last_access": "2025-09-19T09:30:00Z",
    "created_at": "2025-09-18T10:00:00Z",
    "updated_at": "2025-09-19T09:30:00Z"
  }
]
```

### **3. Atualizar Usuário**
```http
PATCH /users?cpf=eq.12345678901
```

**Body:**
```json
{
  "access_level": 2,
  "onboarding_progress": {
    "level": 2,
    "current_step": "profile_complete",
    "completed_steps": ["welcome", "phone_verification"],
    "gamification_score": 150
  },
  "last_access": "2025-09-19T10:00:00Z"
}
```

### **4. Buscar por Telefone**
```http
GET /users?phone=eq.5521981454569
```

### **5. Listar por Nível de Acesso**
```http
GET /users?access_level=eq.7&active=eq.true
```

---

## 🏢 **ENDPOINTS MULTI-ORGANIZACIONAL**

### **1. Criar Organização**
```http
POST /organizations
```

**Body:**
```json
{
  "name": "Associação Gávea Parque",
  "type": "associacao",
  "location_id": 1,
  "metadata": {
    "cnpj": "12345678000199",
    "website": "https://gavea-parque.org.br"
  }
}
```

### **2. Atribuir Papel ao Usuário**
```http
POST /user_organization_roles
```

**Body:**
```json
{
  "user_cpf": "12345678901",
  "organization_id": 1,
  "role_type": "vice_presidente",
  "permissions": {
    "can_manage_members": true,
    "can_create_events": true,
    "can_approve_budgets": false
  }
}
```

### **3. Listar Papéis do Usuário**
```http
GET /user_organization_roles?user_cpf=eq.12345678901&active=eq.true
```

**Response:**
```json
[
  {
    "id": 1,
    "user_cpf": "12345678901",
    "organization_id": 1,
    "role_type": "vice_presidente",
    "permissions": {...},
    "active": true,
    "created_at": "2025-09-18T10:00:00Z"
  },
  {
    "id": 2,
    "user_cpf": "12345678901", 
    "organization_id": 2,
    "role_type": "instrutor",
    "permissions": {...},
    "active": true,
    "created_at": "2025-09-18T11:00:00Z"
  }
]
```

---

## 💬 **ENDPOINTS DE COMUNICAÇÃO**

### **1. Criar Sessão de Chat**
```http
POST /chat_sessions
```

**Body:**
```json
{
  "user_cpf": "12345678901",
  "channel_type": "whatsapp",
  "channel_identifier": "5521981454569",
  "session_data": {
    "context": "onboarding",
    "language": "pt-BR"
  }
}
```

### **2. Enviar Mensagem**
```http
POST /chat_messages
```

**Body:**
```json
{
  "session_id": 1,
  "user_cpf": "12345678901",
  "content": "Olá, preciso de ajuda com meu cadastro",
  "message_type": "text",
  "direction": "inbound",
  "metadata": {
    "pushName": "Lucas Lima",
    "messageId": "msg_123456"
  }
}
```

### **3. Histórico de Mensagens**
```http
GET /chat_messages?session_id=eq.1&order=created_at.desc&limit=50
```

---

## 🎮 **ENDPOINTS DE GAMIFICAÇÃO**

### **1. Atualizar Progresso**
```http
PATCH /users?cpf=eq.12345678901
```

**Body:**
```json
{
  "onboarding_progress": {
    "level": 3,
    "badges": ["first_message", "profile_complete", "community_member"],
    "current_step": "skill_assessment",
    "completed_steps": ["welcome", "phone_verification", "profile_complete"],
    "gamification_score": 350,
    "achievements": [
      {
        "type": "milestone",
        "name": "Primeiro Contato",
        "earned_at": "2025-09-18T10:00:00Z"
      }
    ]
  }
}
```

### **2. Ranking por Pontuação**
```http
GET /users?select=cpf,name,onboarding_progress&order=onboarding_progress->>gamification_score.desc&limit=10
```

---

## 📍 **ENDPOINTS DE LOCALIZAÇÃO**

### **1. Criar Localização**
```http
POST /locations
```

**Body:**
```json
{
  "name": "Rocinha",
  "type": "comunidade",
  "parent_id": 1,
  "coordinates": {
    "lat": -22.9888,
    "lng": -43.2481
  },
  "metadata": {
    "population": 70000,
    "area_km2": 1.43
  }
}
```

### **2. Hierarquia de Localização**
```http
GET /locations?parent_id=eq.1&order=name.asc
```

---

## 🤖 **ENDPOINTS DE AUTOMAÇÃO**

### **1. Criar Processo**
```http
POST /processes
```

**Body:**
```json
{
  "name": "Onboarding Comunitário",
  "description": "Processo automatizado de integração de novos membros",
  "workflow_template": {
    "steps": [
      {"type": "welcome_message", "delay": 0},
      {"type": "profile_completion", "delay": 3600},
      {"type": "skill_assessment", "delay": 86400}
    ]
  },
  "trigger_conditions": {
    "user_access_level": 1,
    "organization_type": "associacao"
  },
  "organization_id": 1
}
```

### **2. Criar Tarefa**
```http
POST /tasks
```

**Body:**
```json
{
  "process_id": 1,
  "assigned_to_cpf": "12345678901",
  "title": "Completar perfil comunitário",
  "description": "Preencher informações sobre sua comunidade e necessidades",
  "priority": 2,
  "due_date": "2025-09-25T23:59:59Z"
}
```

---

## 📊 **QUERIES COMPLEXAS**

### **1. Usuário Completo com Papéis**
```http
GET /users?cpf=eq.12345678901&select=*,user_organization_roles(*,organizations(*))
```

### **2. Estatísticas de Engajamento**
```http
GET /chat_messages?user_cpf=eq.12345678901&select=count
```

### **3. Usuários por Região**
```http
GET /users?geolocation->>municipio=eq.Rio de Janeiro&active=eq.true
```

---

## ⚠️ **CÓDIGOS DE ERRO**

### **Erros Comuns**
```json
{
  "code": "23505",
  "message": "duplicate key value violates unique constraint",
  "details": "Key (cpf)=(12345678901) already exists"
}
```

### **RLS Violation**
```json
{
  "code": "42501",
  "message": "new row violates row-level security policy",
  "hint": "Set app.current_user_cpf context"
}
```

---

## 🔍 **FILTROS E ORDENAÇÃO**

### **Operadores Suportados**
- `eq` - igual
- `neq` - diferente  
- `gt` - maior que
- `gte` - maior ou igual
- `lt` - menor que
- `lte` - menor ou igual
- `like` - contém (case sensitive)
- `ilike` - contém (case insensitive)
- `in` - está em lista
- `is` - é nulo/não nulo

### **Exemplos**
```http
GET /users?access_level=gte.5&active=eq.true&order=created_at.desc&limit=20&offset=0
GET /users?name=ilike.*lucas*&select=cpf,name,phone
GET /chat_messages?created_at=gte.2025-09-18&user_cpf=in.(12345678901,98765432100)
```

---

**🎯 API pronta para produção | ✅ RLS ativo | 🚀 Performance otimizada**
