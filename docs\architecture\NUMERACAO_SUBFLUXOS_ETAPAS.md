# 🔢 NUMERAÇÃO DOS SUBFLUXOS - ETAPAS LÓGICAS DO AI COMTXAE

**Data**: 18/09/2025  
**Objetivo**: Numeração clara e sequencial dos subfluxos  
**Estratégia**: Representar etapas lógicas do fluxo de processamento  

---

## 🎯 FLUXO PRINCIPAL - UNIFIED_USER_PIPELINE

### **WORKFLOW PRINCIPAL (NÍVEL 0)**
**`0. Unified_User_Pipeline`** - **ENTRADA ÚNICA DO SISTEMA**
- **Função**: Webhook único, roteamento por access_level
- **Entrada**: WhatsApp via Evolution API
- **Saída**: Execução sequencial dos 6 subfluxos
- **Responsabilidade**: Ponto de entrada único e distribuição inteligente

---

## 🔄 SUBFLUXOS ESPECIALIZADOS (NÍVEIS 1-6)

### **ETAPA 1 - NORMALIZAÇÃO E EXTRAÇÃO**
**`1. Normalize_Extract_Subflow`** - **PREPARAÇÃO DOS DADOS**
- **Função**: Normalizar e extrair dados de entrada
- **Entrada**: Dados brutos do WhatsApp {from, body, pushName, timestamp}
- **Processamento**: 
  - Normalização de CPF (11 dígitos)
  - Normalização de phone (+55 formato)
  - Limpeza de mensagem (caracteres especiais)
  - Extração de metadados (tipo, origem)
- **Saída**: Dados estruturados e validados
- **Próxima etapa**: → Etapa 2 (User Registry Access)

### **ETAPA 2 - ACESSO E GESTÃO DE USUÁRIOS**
**`2. User_Registry_Access_Subflow`** - **IDENTIFICAÇÃO DO USUÁRIO**
- **Função**: Buscar/criar usuário, validar identidade
- **Entrada**: Dados normalizados da Etapa 1
- **Processamento**:
  - Busca por CPF na tabela users
  - Criação de novo usuário se não existir
  - Determinação de access_level (1-7)
  - Atualização de last_access
- **Saída**: Dados do usuário completos + access_level
- **Próxima etapa**: → Etapa 3 (Session Manager)

### **ETAPA 3 - GERENCIAMENTO DE SESSÕES**
**`3. Session_Manager_Subflow`** - **CONTROLE DE SESSÃO**
- **Função**: Gerenciar sessões de chat e histórico
- **Entrada**: Dados do usuário da Etapa 2
- **Processamento**:
  - Busca por sessão ativa (< 30 min)
  - Criação de nova sessão se necessário
  - Inserção da mensagem no histórico
  - Atualização de last_activity
- **Saída**: Dados da sessão + histórico atualizado
- **Próxima etapa**: → Etapa 4 (Onboarding Orchestrator)

### **ETAPA 4 - ORQUESTRAÇÃO DO ONBOARDING**
**`4. Onboarding_Orchestrator_Subflow`** - **PROGRESSÃO GAMIFICADA**
- **Função**: Gerenciar onboarding e progressão de níveis
- **Entrada**: Dados da sessão da Etapa 3
- **Processamento**:
  - Análise do progresso atual (onboarding_progress)
  - Roteamento por access_level (1-7)
  - Lógica específica para cada nível
  - Atualização de badges e conquistas
- **Saída**: Contexto personalizado + próximas ações
- **Próxima etapa**: → Etapa 5 (Message Processor)

### **ETAPA 5 - PROCESSAMENTO DE MENSAGENS**
**`5. Message_Processor_Subflow`** - **INTELIGÊNCIA ARTIFICIAL**
- **Função**: Processar mensagem com IA e gerar resposta
- **Entrada**: Contexto personalizado da Etapa 4
- **Processamento**:
  - Categorização da mensagem (greeting, question, help, etc.)
  - Chamada para OpenAI com contexto completo
  - Processamento da resposta da IA
  - Envio via Evolution API
- **Saída**: Resposta enviada + log da interação
- **Próxima etapa**: → Etapa 6 (Persistence Memory)

### **ETAPA 6 - PERSISTÊNCIA E MEMÓRIA**
**`6. Persistence_Memory_Subflow`** - **CONSOLIDAÇÃO FINAL**
- **Função**: Persistir memória, contexto e finalizar ciclo
- **Entrada**: Log da interação da Etapa 5
- **Processamento**:
  - Persistência no chat_memory_history
  - Atualização do user_qa_context
  - Consolidação de dados da sessão
  - Geração de métricas de atividade
- **Saída**: Dados persistidos + ciclo completo
- **Próxima etapa**: → FIM (aguarda nova mensagem)

---

## 🔄 FLUXO SEQUENCIAL COMPLETO

### **DIAGRAMA DE FLUXO**
```
WhatsApp Message
       ↓
┌─────────────────────────────────────────────────────────────┐
│  0. Unified_User_Pipeline (WEBHOOK ÚNICO)                  │
│     • Recebe mensagem                                       │
│     • Extrai dados básicos                                 │
│     • Roteia por access_level                              │
└─────────────────────────────────────────────────────────────┘
       ↓
┌─────────────────────────────────────────────────────────────┐
│  1. Normalize_Extract_Subflow (PREPARAÇÃO)                 │
│     • Normaliza CPF, phone                                 │
│     • Limpa mensagem                                       │
│     • Extrai metadados                                     │
└─────────────────────────────────────────────────────────────┘
       ↓
┌─────────────────────────────────────────────────────────────┐
│  2. User_Registry_Access_Subflow (IDENTIFICAÇÃO)           │
│     • Busca/cria usuário                                   │
│     • Determina access_level                               │
│     • Atualiza last_access                                 │
└─────────────────────────────────────────────────────────────┘
       ↓
┌─────────────────────────────────────────────────────────────┐
│  3. Session_Manager_Subflow (SESSÃO)                       │
│     • Gerencia sessão ativa                                │
│     • Insere mensagem no histórico                         │
│     • Atualiza metadados                                   │
└─────────────────────────────────────────────────────────────┘
       ↓
┌─────────────────────────────────────────────────────────────┐
│  4. Onboarding_Orchestrator_Subflow (GAMIFICAÇÃO)          │
│     • Analisa progresso                                    │
│     • Personaliza por nível                                │
│     • Atualiza badges                                      │
└─────────────────────────────────────────────────────────────┘
       ↓
┌─────────────────────────────────────────────────────────────┐
│  5. Message_Processor_Subflow (IA)                         │
│     • Categoriza mensagem                                  │
│     • Processa com OpenAI                                  │
│     • Envia resposta                                       │
└─────────────────────────────────────────────────────────────┘
       ↓
┌─────────────────────────────────────────────────────────────┐
│  6. Persistence_Memory_Subflow (CONSOLIDAÇÃO)              │
│     • Persiste memória                                     │
│     • Atualiza contexto                                    │
│     • Finaliza ciclo                                       │
└─────────────────────────────────────────────────────────────┘
       ↓
   Resposta Enviada
```

---

## 📊 MÉTRICAS POR ETAPA

### **TEMPOS ESPERADOS**
- **Etapa 0**: < 100ms (roteamento)
- **Etapa 1**: < 200ms (normalização)
- **Etapa 2**: < 300ms (busca/criação usuário)
- **Etapa 3**: < 200ms (gestão sessão)
- **Etapa 4**: < 300ms (lógica onboarding)
- **Etapa 5**: < 2000ms (IA + envio)
- **Etapa 6**: < 300ms (persistência)
- **TOTAL**: < 3400ms (objetivo: < 3000ms)

### **DADOS TRANSFERIDOS ENTRE ETAPAS**
```javascript
// Estrutura de dados entre etapas
const flowData = {
  // Etapa 0 → 1
  raw: { from, body, pushName, timestamp },
  
  // Etapa 1 → 2  
  normalized: { cpf, phone, message, metadata },
  
  // Etapa 2 → 3
  user: { cpf, access_level, onboarding_progress, active },
  
  // Etapa 3 → 4
  session: { session_id, user_cpf, message_count, last_activity },
  
  // Etapa 4 → 5
  context: { personalized_prompt, current_level, next_actions },
  
  // Etapa 5 → 6
  interaction: { ai_response, sent_message, category, timestamp },
  
  // Etapa 6 → FIM
  completed: { memory_saved, context_updated, metrics_generated }
};
```

---

## 🎯 WORKFLOW ADICIONAL (NÍVEL 7)

### **UTILITÁRIO - GERAÇÃO DE SCHEMA**
**`7. Database_Schema_Generator`** - **MANUTENÇÃO DO SISTEMA**
- **Função**: Gerar e manter schema do banco de dados
- **Execução**: Sob demanda (não faz parte do fluxo principal)
- **Responsabilidade**: Migração, validação, documentação do schema

---

## ✅ BENEFÍCIOS DA NUMERAÇÃO

### **PARA DESENVOLVEDORES**
- **Clareza**: Ordem lógica de execução
- **Debug**: Identificação rápida de etapas com problema
- **Manutenção**: Fácil localização de funcionalidades

### **PARA AGENTES REMOTOS**
- **Especialização**: Cada agente foca em uma etapa específica
- **Dependências**: Compreensão clara das entradas/saídas
- **Qualidade**: Implementação detalhada por etapa

### **PARA O SISTEMA**
- **Performance**: Otimização por etapa
- **Monitoramento**: Métricas específicas por subfluxo
- **Escalabilidade**: Paralelização onde possível

---

**Status**: Numeração completa dos subfluxos criada  
**Próximo passo**: Usar esta numeração nos prompts dos agentes remotos  
**Impacto**: Compreensão clara das etapas do fluxo de processamento
