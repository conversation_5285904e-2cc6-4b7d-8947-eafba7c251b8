{"name": "whatsapp-ai-assistant-ecosystem", "version": "1.0.0", "description": "Multi-Hierarchical Agent Ecosystem for WhatsApp AI Assistant with n8n workflows", "main": "automation/deploy_ecosystem.js", "scripts": {"deploy": "node automation/deploy_ecosystem.js", "test": "node automation/test_ecosystem.js", "validate": "node automation/validate_workflows.js", "start": "npm run deploy && npm run test", "dev": "npm run deploy", "lint": "eslint automation/*.js", "format": "prettier --write automation/*.js", "docs": "jsdoc automation/*.js -d docs/api", "clean": "rm -rf deployment_report_*.json test_report_*.json", "reorganize": "node scripts/orchestrate_reorganization.js", "reorganize:local": "bash scripts/reorganize_project_structure.sh", "reorganize:sync": "node scripts/n8n_sync_workflows.js", "reorganize:validate": "node scripts/validate_workflow_integrity.js", "reorganize:rollback": "bash scripts/rollback_reorganization.sh", "reorganize:test": "node scripts/test_scripts.js", "sync:from-n8n": "node scripts/sync_from_n8n.js", "sync:to-n8n": "node scripts/sync_to_n8n.js", "sync:full": "node scripts/sync_bidirectional.js", "workflows:validate": "node scripts/validate_workflow_integrity.js", "workflows:organize": "node scripts/organize_workflows.js", "git:commit-workflows": "git add workflows/active/ && git commit -m 'update: sync workflows' && git push"}, "keywords": ["whatsapp", "ai", "assistant", "n8n", "workflows", "automation", "red-cross", "humanitarian", "chatbot", "multi-agent"], "author": {"name": "AI Comtxae Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/AI-Comtxae.git"}, "bugs": {"url": "https://github.com/your-org/AI-Comtxae/issues"}, "homepage": "https://github.com/your-org/AI-Comtxae#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"chalk": "^4.1.2", "commander": "^11.1.0", "dotenv": "^16.6.1", "inquirer": "^9.2.12", "node-fetch": "^3.3.2", "ora": "^7.0.1", "pg": "^8.16.3"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "jsdoc": "^4.0.2", "nodemon": "^3.0.2", "prettier": "^3.1.0"}, "bin": {"deploy-ecosystem": "./automation/deploy_ecosystem.js", "test-ecosystem": "./automation/test_ecosystem.js"}, "files": ["automation/", "product_flows/", "infrastructure_flows/", "docs/", "README.md", "LICENSE"], "config": {"n8n_api_url": "http://localhost:5678", "deployment_timeout": 300000, "test_timeout": 120000}, "eslintConfig": {"env": {"node": true, "es2021": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "rules": {"no-console": "off", "no-unused-vars": "warn", "prefer-const": "error"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "collectCoverageFrom": ["automation/**/*.js", "!automation/deploy_ecosystem.js", "!automation/test_ecosystem.js"]}}