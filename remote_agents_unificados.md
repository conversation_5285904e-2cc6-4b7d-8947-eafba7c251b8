# 🤖 **REMOTE AGENTS UNIFICADOS - AI COMTXAE (8 AGENTES)**

**Data**: 19/09/2025
**Objetivo**: 8 agentes especializados para workflows funcionais
**Estratégia**: Implementação em etapas (não "one-shot")
**Status**: ✅ REPOSITÓRIO ORGANIZADO - PRONTO PARA IMPLEMENTAÇÃO

---

## 📊 **STATUS ATUAL DO SISTEMA (IMPORTANTE)**

### **ARQUITETURA VALIDADA E LIMPA**
- ✅ **Agent 6 - Workflow Cleaner**: EXECUTADO COM SUCESSO (PR #2)
  - 6 workflows problemáticos removidos (59 → 53 workflows)
  - Hierarquia estabelecida: Unified_User_Pipeline → 6 subfluxos
- ✅ **Agent 7 - Architecture Validator**: EXECUTADO COM SUCESSO (PRs #3, #4)
  - 100% conformidade arquitetural alcançada
  - Webhook único garantido (1 ativo de 21 total)

### **WORKFLOWS ATUAIS**
- ✅ **Unified_User_Pipeline**: ATIVO (webhook único)
- ⚠️ **6 subfluxos**: INATIVOS (aguardando implementação)
- ⚠️ **Database_Schema_Generator**: INATIVO (aguardando implementação)

### **SUPABASE CONFIRMADO**
- ✅ **20 tabelas validadas**
- ✅ **Schema unificado**: CPF como identificador único
- ✅ **Configurações funcionais**: URL e keys validadas

---

## 🔧 **CONFIGURAÇÃO GLOBAL PARA TODOS OS AGENTES**

### **INFORMAÇÕES DO PROJETO**
- **Repositório**: https://github.com/lucasblima/AI-Comtxae
- **Projeto Supabase**: assistente_v0.1 (ID: qvkstxeayyzrntywifdi)
- **Região**: sa-east-1 (Brasil)
- **Status atual**: 20 tabelas, schema unificado implementado

### **DECISÕES ARQUITETURAIS APROVADAS**
1. **CPF como identificador único principal** (não phone)
2. **Sistema híbrido**: access_level global (1-7) + roles específicos
3. **Múltiplos phones por CPF** permitidos
4. **Onboarding gamificado** com 7 níveis de progressão
5. **Webhook único**: Apenas Unified_User_Pipeline
6. **Subfluxos especializados**: 6 workflows via Execute Workflow nodes

### **CONFIGURAÇÃO N8N API**
- **N8N URL**: https://n8n-n8n.w9jo16.easypanel.host
- **API Key**: ⚠️ EXPIRADA - Solicitar nova API key antes de implementar
- **Cliente API**: automation/maintenance/n8n_api_client.js (disponível no repositório)

### **CONFIGURAÇÃO SUPABASE**
- **SUPABASE_URL**: https://qvkstxeayyzrntywifdi.supabase.co
- **SUPABASE_PUBLISHABLE_KEY**: sb_publishable_HbyJoVzTKe9bW_NitZQw_A_IWtkKCgB
- **SUPABASE_KEY**: sb_secret_i8KnLbb6utU4LntRKt_pxg_Hc8OcMQS

### **USO OBRIGATÓRIO DO MCP CONTEXT 7**
**ANTES DE CRIAR QUALQUER NÓ n8n, SEMPRE:**
1. **Consultar Context 7** com library ID: `/n8n-io/n8n-docs`
2. **Verificar tipos de nós atuais** disponíveis na versão atual
3. **Validar configurações** de parâmetros e autenticação
4. **Confirmar compatibilidade** antes de implementar

**NUNCA usar nós deprecated como**:
- `n8n-nodes-base.openAi` (usar `n8n-nodes-base.httpRequest` com OpenAI API)
- Tipos de nós não validados pelo Context 7

### **REGRAS CRÍTICAS DE IMPLEMENTAÇÃO**
1. **Execução recorrente**: Execute até o workflow estar 100% funcional - não pare na primeira tentativa
2. **Nomenclatura**: Se workflow existe com nome incorreto, RENOMEIE ou crie novo com nome correto
3. **Python obrigatório**: TODOS os nós Code devem usar Python (não JavaScript)
4. **Nós Supabase**: Apenas para CRUD simples (create, get, update, delete)
5. **Nós Postgres**: Para queries SQL customizadas e complexas
6. **Conexões obrigatórias**: Todos os nós devem estar conectados funcionalmente
7. **Teste individual**: Cada workflow deve funcionar isoladamente
8. **Dados consistentes**: Preservar todos os dados originais entre nós

---

## 🎯 **AGENT 1 - 01_UNIFIED_USER_PIPELINE**

### **CONTEXTO**
Você é o Unified User Pipeline Agent do projeto AI Comtxae. Sua missão é criar o workflow PRINCIPAL do sistema - o único com webhook e responsável pelo roteamento de todos os usuários baseado em access_level.

**IMPORTANTE**: Execute de forma RECORRENTE até o workflow estar 100% funcional. Já existe um workflow 01_Unified_User_Pipeline (#ybV3msRSWJcVjxoA).

### **TAREFAS ESPECÍFICAS**
1. **Criar workflow "Unified_User_Pipeline" via n8n API**:
   - **PRIMEIRO**: Consultar Context 7 para tipos de nós Webhook atuais
   - Usar N8nApiClient para criação programática
   - **Webhook Trigger**: Único webhook do sistema (Evolution API)
   - **Code Node**: Extração de CPF, phone, access_level, pushName
   - **Switch Node**: Roteamento por access_level (1-7)
   - **Execute Workflow Nodes** (6x): Um para cada subfluxo

2. **ESTRUTURA CORRIGIDA - APENAS EXECUTE WORKFLOW NODES**:

   **IMPORTANTE**: Agent 1 NÃO deve processar dados. Apenas rotear para subfluxos.

   **Nós necessários**:
   - **Webhook Trigger**: Recebe dados do Evolution API
   - **Execute Workflow Node 1**: 02_Normalize_Extract_Subflow
   - **Execute Workflow Node 2**: 03_User_Registry_Access_Subflow
   - **Execute Workflow Node 3**: 04_Session_Manager_Subflow
   - **Execute Workflow Node 4**: 05_Onboarding_Orchestrator_Subflow
   - **Execute Workflow Node 5**: 06_Message_Processor_Subflow
   - **Execute Workflow Node 6**: 07_Persistence_Memory_Subflow
   - **Webhook Response**: Retorna status 200

   **REMOVER COMPLETAMENTE**:
   - ❌ Code Nodes para extração de dados
   - ❌ Switch Nodes para roteamento por access_level
   - ❌ Qualquer processamento de CPF (não existe no webhook)
   - ❌ Funções extract_cpf(), get_user_access_level()

3. **Configurar conexões dos subfluxos**:
   - Normalize_Extract_Subflow (Etapa 1)
   - User_Registry_Access_Subflow (Etapa 2)
   - Session_Manager_Subflow (Etapa 3)
   - Onboarding_Orchestrator_Subflow (Etapa 4)
   - Message_Processor_Subflow (Etapa 5)
   - Persistence_Memory_Subflow (Etapa 6)

### **CRITÉRIOS DE SUCESSO**
- Único webhook ativo no sistema (validação arquitetural)
- Roteamento correto por access_level (1-7)
- Execução sequencial de todos os 6 subfluxos
- Dados passados corretamente entre workflows
- Tempo de resposta < 3 segundos

### **ENTREGÁVEIS**
1. Workflow "Unified_User_Pipeline" criado e ativo no n8n
2. Lógica de roteamento implementada e testada
3. Conexões com os 6 subfluxos configuradas
4. Validação de dados de entrada/saída
5. Documentação das regras de roteamento

---

## 🎯 **AGENT 2 - NORMALIZE_EXTRACT_SUBFLOW**

### **CONTEXTO**
Você é o Normalize Extract Agent do projeto AI Comtxae. Sua missão é criar o primeiro subfluxo do sistema, responsável pela normalização e extração de dados de entrada do WhatsApp.

**IMPORTANTE**: Execute de forma RECORRENTE até o workflow estar 100% funcional. Já existe um workflow 02_Normalize_Extract_Subflow (#9bMvISktOxQkzwtj).

### **TAREFAS ESPECÍFICAS**
1. **Criar workflow "Normalize_Extract_Subflow" via n8n API**:
   - **PRIMEIRO**: Consultar Context 7 para tipos de nós Code atuais
   - **Manual Trigger**: Subfluxo não tem webhook
   - **Code Node**: Normalização de CPF, phone, mensagem
   - **Set Node**: Estruturação de dados padronizada
   - **Merge Node**: Consolidação de dados
   - **Code Node**: Validação final e limpeza

2. **Implementar normalização de dados (JAVASCRIPT PURO)**:
   ```javascript
   // Normalização de dados disponíveis do webhook - USAR JAVASCRIPT
   for (item of _input.all()) {
       // Normalizar telefone (único dado para identificação)
       let rawPhone = item.json.from || '';
       let cleanPhone = rawPhone.replace(/\D/g, ''); // Remove não-dígitos

       if (!cleanPhone.startsWith('55')) {
           cleanPhone = '55' + cleanPhone;
       }

       // Normalizar mensagem baseada no tipo
       let messageType = item.json.messageType || 'text';
       let content = '';

       if (messageType === 'text') {
           content = (item.json.body || '').trim();
       } else if (['image', 'video', 'audio', 'document'].includes(messageType)) {
           content = item.json.caption || `[${messageType.toUpperCase()}]`;

           // Preservar informações de mídia
           item.json.mediaInfo = {
               url: item.json.mediaUrl,
               type: messageType,
               mimeType: item.json.mimeType,
               size: item.json.mediaSize,
               duration: item.json.duration
           };
       }

       // Dados normalizados (SEM CPF - não existe no webhook)
       item.json.phone = '+' + cleanPhone;
       item.json.message = content;
       item.json.name = item.json.pushName || '';
       item.json.messageType = messageType;
       item.json.normalized_at = new Date().toISOString();

       // Preservar dados originais
       item.json.original = {
           from: item.json.from,
           body: item.json.body,
           pushName: item.json.pushName,
           timestamp: item.json.timestamp
       };
   }

   return _input.all();
   ```

3. **Validações obrigatórias CORRIGIDAS**:
   - ❌ ~~CPF: Formato 11 dígitos~~ (CPF NÃO existe no webhook)
   - ✅ Phone: Formato internacional (+55)
   - ✅ Message: Limpeza de caracteres especiais
   - ✅ Timestamp: ISO 8601 format usando new Date().toISOString()
   - ✅ MessageType: Diferenciação entre text, audio, image, video

### **CRITÉRIOS DE SUCESSO**
- Dados normalizados corretamente (100% dos casos)
- Validação de CPF e phone funcionais
- Extração de metadados completa
- Estrutura consistente de saída
- Performance < 500ms por execução

### **ENTREGÁVEIS**
1. Workflow "Normalize_Extract_Subflow" criado e ativo
2. Funções de normalização implementadas
3. Validações de dados funcionais
4. Estrutura padronizada de saída
5. Testes unitários das funções de normalização

---

## 🎯 **AGENT 3 - USER_REGISTRY_ACCESS_SUBFLOW**

### **CONTEXTO**
Você é o User Registry Access Agent do projeto AI Comtxae. Sua missão é criar o segundo subfluxo do sistema, responsável pelo acesso e gestão de usuários no banco de dados unificado.

**IMPORTANTE**: Execute de forma RECORRENTE até o workflow estar 100% funcional. Se já existe um workflow 03_User_Registry_Access_Subflow (#QmdVie0kPrUCx5VL).

### **TAREFAS ESPECÍFICAS**
1. **Criar workflow "User_Registry_Access_Subflow" via n8n API**:
   - **PRIMEIRO**: Consultar Context 7 para tipos de nós Postgres e Supabase atuais
   - **Manual Trigger**: Recebe dados do subfluxo anterior
   - **Postgres Node**: Query complexa para buscar usuário por CPF
   - **IF Node**: Usuário existe?
   - **Supabase Node**: Criar novo usuário (se não existe)
   - **Supabase Node**: Atualizar last_access
   - **Code Node**: Determinar access_level e permissions

2. **Implementar queries de usuário**:
   ```sql
   -- Buscar usuário completo por CPF
   SELECT u.cpf, u.access_level, u.onboarding_progress, u.active,
          COUNT(cm.id) as message_count,
          MAX(cs.last_activity) as last_session
   FROM users u
   LEFT JOIN chat_sessions cs ON u.cpf = cs.user_cpf
   LEFT JOIN chat_messages cm ON cs.id = cm.session_id
   WHERE u.cpf = $1
   GROUP BY u.cpf, u.access_level, u.onboarding_progress, u.active;
   ```

3. **Criar usuário padrão (PYTHON)**:
   ```python
   # Estrutura de novo usuário - USAR PYTHON
   from datetime import datetime
   import json

   def create_new_user(normalized_cpf, normalized_phone, push_name):
       new_user = {
           'cpf': normalized_cpf,
           'phone': normalized_phone,
           'name': push_name or 'Usuário',
           'access_level': 1,  # Iniciante
           'onboarding_progress': {
               'level': 1,
               'current_step': 'welcome',
               'completed_steps': [],
               'badges': [],
               'gamification_score': 0
           },
           'active': True,
           'metadata': {
               'first_contact': datetime.now().isoformat(),
               'source': 'whatsapp'
           }
       }
       return new_user

   # Usar a função
   input_data = _input.all()[0]['json']
   new_user = create_new_user(
       input_data.get('cpf'),
       input_data.get('phone'),
       input_data.get('pushName')
   )

   return new_user
   ```

### **CRITÉRIOS DE SUCESSO**
- Busca eficiente por CPF (< 100ms)
- Criação de novos usuários funcionais
- Validação de access_level correta
- Atualização de last_access automática
- Tratamento de erros de conexão

### **ENTREGÁVEIS**
1. Workflow "User_Registry_Access_Subflow" criado e ativo
2. Queries otimizadas para busca de usuários
3. Lógica de criação de novos usuários
4. Sistema de validação de access_level
5. Tratamento de erros e exceções

---

## 🎯 **AGENT 4 - SESSION_MANAGER_SUBFLOW**

### **CONTEXTO**
Você é o Session Manager Agent do projeto AI Comtxae. Sua missão é criar o terceiro subfluxo do sistema, responsável pelo gerenciamento de sessões de chat e histórico de mensagens.

**IMPORTANTE**: Execute de forma RECORRENTE até o workflow estar 100% funcional. Se já existe um workflow 04_Session_Manager_Subflow (#BfbQRJX6Wu1Dig5v).

### **TAREFAS ESPECÍFICAS**
1. **Criar workflow "Session_Manager_Subflow" via n8n API**:
   - **PRIMEIRO**: Consultar Context 7 para tipos de nós atuais
   - **Manual Trigger**: Recebe dados dos subfluxos anteriores
   - **Postgres Node**: Buscar sessão ativa do usuário
   - **IF Node**: Sessão existe e está ativa?
   - **Supabase Node**: Criar nova sessão (se necessário)
   - **Supabase Node**: Inserir mensagem na sessão
   - **Code Node**: Atualizar last_activity e metadados

2. **Implementar gestão de sessões**:
   ```sql
   -- Buscar sessão ativa
   SELECT id, user_cpf, status, last_activity, metadata
   FROM chat_sessions 
   WHERE user_cpf = $1 
     AND status = 'active'
     AND last_activity > NOW() - INTERVAL '30 minutes'
   ORDER BY last_activity DESC
   LIMIT 1;
   ```

3. **Criar nova sessão quando necessário (PYTHON)**:
   ```python
   # Nova sessão de chat - USAR PYTHON
   from datetime import datetime

   def create_new_session(user_cpf, user_access_level):
       new_session = {
           'user_cpf': user_cpf,
           'status': 'active',
           'started_at': datetime.now().isoformat(),
           'last_activity': datetime.now().isoformat(),
           'metadata': {
               'access_level': user_access_level,
               'device': 'whatsapp',
               'session_type': 'chat'
           }
       }
       return new_session

   # Usar a função
   input_data = _input.all()[0]['json']
   new_session = create_new_session(
       input_data.get('user_cpf'),
       input_data.get('access_level')
   )

   return new_session
   ```

### **CRITÉRIOS DE SUCESSO**
- Gestão de sessões ativas eficiente
- Histórico de mensagens completo
- Controle de timeout de sessão (30 min)
- Metadados de sessão atualizados
- Performance otimizada para múltiplos usuários

### **ENTREGÁVEIS**
1. Workflow "Session_Manager_Subflow" criado e ativo
2. Sistema de gestão de sessões implementado
3. Controle de timeout automático
4. Histórico de mensagens funcional
5. Metadados de sessão estruturados

---

## 🎯 **AGENT 5 - ONBOARDING_ORCHESTRATOR_SUBFLOW**

### **CONTEXTO**
Você é o Onboarding Orchestrator Agent do projeto AI Comtxae. Sua missão é criar o quarto subfluxo do sistema, responsável pela orquestração do processo de onboarding gamificado com 7 níveis de progressão.

**IMPORTANTE**: Execute de forma RECORRENTE até o workflow estar 100% funcional. Já existe um workflow 05_Onboarding_Orchestrator_Subflow (#3gTFrHtPzU9q6N0d).

### **TAREFAS ESPECÍFICAS**
1. **Criar workflow "Onboarding_Orchestrator_Subflow" via n8n API**:
   - **PRIMEIRO**: Consultar Context 7 para tipos de nós Switch atuais
   - **Manual Trigger**: Recebe dados dos subfluxos anteriores
   - **Code Node**: Analisar progresso atual do usuário
   - **Switch Node**: Roteamento por access_level (1-7)
   - **Code Nodes** (7x): Lógica específica para cada nível
   - **Supabase Node**: Atualizar onboarding_progress
   - **Merge Node**: Consolidar resposta personalizada

2. **Implementar sistema de níveis gamificado (PYTHON)**:
   ```python
   # Lógica de progressão por nível - USAR PYTHON
   def get_onboarding_logic():
       onboarding_logic = {
           1: {  # Iniciante
               'welcome_message': "Bem-vindo ao AI Comtxae! Vamos começar sua jornada.",
               'required_actions': ['complete_profile', 'first_interaction'],
               'badges_available': ['newcomer'],
               'next_level_criteria': 'complete_profile and first_interaction'
           },
           2: {  # Explorador
               'welcome_message': "Ótimo! Agora vamos explorar suas necessidades.",
               'required_actions': ['answer_survey', 'set_preferences'],
               'badges_available': ['explorer'],
               'next_level_criteria': 'survey_completed and preferences_set'
           },
           3: {  # Participante
               'welcome_message': "Excelente! Agora você pode participar ativamente.",
               'required_actions': ['join_community', 'first_contribution'],
               'badges_available': ['participant'],
               'next_level_criteria': 'community_joined and contribution_made'
           }
           # ... adicionar níveis 4-7
       }
       return onboarding_logic

   # Usar a função
   input_data = _input.all()[0]['json']
   current_level = input_data.get('access_level', 1)
   logic = get_onboarding_logic()
   current_logic = logic.get(current_level, logic[1])

   return current_logic
   ```

3. **Sistema de badges e conquistas (PYTHON)**:
   ```python
   # Atualizar progresso gamificado - USAR PYTHON
   def update_gamification_progress(current_level, next_step, completed_steps, existing_badges, new_badge, user_actions):
       update_progress = {
           'level': current_level,
           'current_step': next_step,
           'completed_steps': completed_steps + [current_step],
           'badges': existing_badges + [new_badge] if new_badge else existing_badges,
           'gamification_score': calculate_score(user_actions),
           'achievements': check_achievements(user_actions)
       }
       return update_progress

   # Usar a função
   input_data = _input.all()[0]['json']
   progress_update = update_gamification_progress(
       input_data.get('current_level'),
       input_data.get('next_step'),
       input_data.get('completed_steps', []),
       input_data.get('existing_badges', []),
       input_data.get('new_badge'),
       input_data.get('user_actions', [])
   )

   return progress_update
   ```

### **CRITÉRIOS DE SUCESSO**
- Progressão por níveis 1-7 funcionais
- Sistema de gamificação implementado
- Badges e conquistas automáticas
- Personalização por nível de acesso
- Motivação e engajamento do usuário

### **ENTREGÁVEIS**
1. Workflow "Onboarding_Orchestrator_Subflow" criado e ativo
2. Sistema de 7 níveis implementado
3. Lógica de gamificação funcional
4. Sistema de badges e conquistas
5. Personalização de mensagens por nível

---

## 🎯 **AGENT 6 - MESSAGE_PROCESSOR_SUBFLOW**

### **CONTEXTO**
Você é o Message Processor Agent do projeto AI Comtxae. Sua missão é criar o quinto subfluxo do sistema, responsável pelo processamento inteligente de mensagens usando IA e geração de respostas contextualizadas.

**IMPORTANTE**: Execute de forma RECORRENTE até o workflow estar 100% funcional. Já existe um workflow com nome 06_Message_Processor_Subflow (#XNJfepWrkBExfkcF).

### **TAREFAS ESPECÍFICAS**
1. **Criar workflow "Message_Processor_Subflow" via n8n API**:
   - **PRIMEIRO**: Consultar Context 7 para tipos de nós HTTP atuais
   - **Manual Trigger**: Recebe dados dos subfluxos anteriores
   - **Code Node**: Análise e categorização da mensagem
   - **HTTP Request Node**: Chamada para OpenAI API
   - **Code Node**: Processamento da resposta da IA
   - **HTTP Request Node**: Envio via Evolution API
   - **Code Node**: Log da interação

2. **Implementar processamento diferenciado por tipo (JAVASCRIPT PURO)**:

   **ESTRUTURA CORRIGIDA**:
   - **Switch Node**: Por messageType (text, audio, image, video)
   - **Rota TEXTO**: Categorização + OpenAI API
   - **Rota MÍDIA**: Resposta automática (templates)

   **A. Code Node - Categorização (apenas para TEXTO)**:
   ```javascript
   // Categorização de mensagens de texto (SEM imports)
   for (item of _input.all()) {
       let message = (item.json.message || '').toLowerCase();
       let category = 'general';

       // Categorização usando métodos nativos JavaScript
       if (message.match(/^(oi|olá|bom dia|boa tarde|boa noite)/)) {
           category = 'greeting';
       } else if (message.includes('?')) {
           category = 'question';
       } else if (message.match(/(ajuda|help|socorro|não sei)/)) {
           category = 'help_request';
       } else if (message.match(/(problema|erro|não funciona|bug)/)) {
           category = 'complaint';
       } else if (message.match(/(obrigado|valeu|parabéns|excelente)/)) {
           category = 'compliment';
       }

       item.json.message_category = category;
       item.json.processing_type = 'ai';
   }

   return _input.all();
   ```

   **B. Code Node - Resposta Automática ÁUDIO**:
   ```javascript
   // Resposta automática para áudio
   for (item of _input.all()) {
       item.json.ai_response = "🎵 Recebi seu áudio! Por favor, descreva o que você gostaria de saber em texto para que eu possa ajudar melhor.";
       item.json.processing_type = 'template';
       item.json.message_category = 'media_audio';
   }

   return _input.all();
   ```

   **C. Code Node - Resposta Automática IMAGEM**:
   ```javascript
   // Resposta automática para imagem
   for (item of _input.all()) {
       let response = "📸 Vi sua imagem! ";

       if (item.json.message && item.json.message !== '[IMAGE]') {
           response += `Sobre "${item.json.message}", posso ajudar com informações específicas.`;
       } else {
           response += "Pode me contar o que gostaria de saber sobre ela?";
       }

       item.json.ai_response = response;
       item.json.processing_type = 'template';
       item.json.message_category = 'media_image';
   }

   return _input.all();
   ```

3. **Integração com OpenAI**:
   ```javascript
   // Prompt contextualizado para IA
   const aiPrompt = `
   Contexto: Usuário nível ${accessLevel} do AI Comtxae
   Histórico: ${recentMessages}
   Mensagem: ${userMessage}

   Responda de forma personalizada, considerando:
   - Nível de acesso do usuário
   - Contexto da conversa
   - Objetivos do onboarding
   - Tom amigável e profissional
   `;
   ```

### **CRITÉRIOS DE SUCESSO**
- Categorização precisa de mensagens
- Respostas contextualizadas da IA
- Integração funcional com OpenAI
- Envio automático via Evolution API
- Log completo das interações

### **ENTREGÁVEIS**
1. Workflow "Message_Processor_Subflow" criado e ativo
2. Sistema de categorização de mensagens
3. Integração com OpenAI API funcional
4. Envio automático de respostas
5. Sistema de logging de interações

---

## 🎯 **AGENT 7 - PERSISTENCE_MEMORY_SUBFLOW**

### **CONTEXTO**
Você é o Persistence Memory Agent do projeto AI Comtxae. Sua missão é criar o sexto subfluxo do sistema, responsável pela persistência de memória, contexto e histórico de todas as interações.

**IMPORTANTE**: Execute de forma RECORRENTE até o workflow estar 100% funcional. Já existe um workflow com nome Persistence_Memory_Subflow (#x0cXaiDeMsYmAs9E).

### **TAREFAS ESPECÍFICAS**
1. **Criar workflow "Persistence_Memory_Subflow" via n8n API**:
   - **PRIMEIRO**: Consultar Context 7 para tipos de nós Postgres atuais
   - **Manual Trigger**: Recebe dados de todos os subfluxos anteriores
   - **Postgres Node**: Queries complexas para histórico
   - **Supabase Node**: Persistir memória de contexto
   - **Code Node**: Atualizar índices de busca
   - **Postgres Node**: Consolidar dados de sessão
   - **Code Node**: Gerar relatórios de atividade

2. **Implementar persistência de memória**:
   ```sql
   -- Inserir no histórico de memória
   INSERT INTO chat_memory_history (
     session_id, user_cpf, memory_type, content,
     context_data, created_at, metadata
   ) VALUES (
     $1, $2, $3, $4, $5, NOW(), $6
   );
   ```

3. **Sistema de contexto inteligente (PYTHON)**:
   ```python
   # Estrutura de contexto - USAR PYTHON
   from datetime import datetime
   import json

   def create_context_data(input_data):
       context_data = {
           'user_profile': {
               'access_level': input_data.get('access_level'),
               'preferences': input_data.get('preferences', {}),
               'interaction_history': input_data.get('recent_interactions', [])
           },
           'conversation_context': {
               'topic': input_data.get('current_topic'),
               'sentiment': analyze_sentiment(input_data.get('messages', [])),
               'intent': detect_intent(input_data.get('last_message'))
           },
           'session_metadata': {
               'duration': calculate_session_duration(input_data.get('session_start')),
               'message_count': input_data.get('message_count', 0),
               'engagement_score': calculate_engagement(input_data)
           },
           'timestamp': datetime.now().isoformat()
       }
       return context_data

   def analyze_sentiment(messages):
       # Implementar análise de sentimento básica
       return 'neutral'

   def detect_intent(message):
       # Implementar detecção de intenção
       return 'general'

   def calculate_engagement(data):
       # Calcular score de engajamento
       return 0.5

   # Usar a função
   input_data = _input.all()[0]['json']
   context = create_context_data(input_data)

   return context
   ```

### **CRITÉRIOS DE SUCESSO**
- Persistência completa de memória
- Contexto inteligente mantido
- Histórico estruturado e pesquisável
- Performance otimizada para consultas
- Integridade referencial garantida

### **ENTREGÁVEIS**
1. Workflow "Persistence_Memory_Subflow" criado e ativo
2. Sistema de persistência de memória
3. Contexto inteligente implementado
4. Histórico estruturado e pesquisável
5. Relatórios de atividade automáticos

---

## 🎯 **AGENT 8 - DATABASE_SCHEMA_GENERATOR**

### **CONTEXTO**
Você é o Database Schema Generator Agent do projeto AI Comtxae. Sua missão é criar o workflow utilitário responsável pela geração e manutenção automática do schema do banco de dados.

**IMPORTANTE**: Execute de forma RECORRENTE até o workflow estar 100% funcional. Se já existe um workflow com nome similar, RENOMEIE-O ou crie um novo com o nome correto "Database_Schema_Generator".

### **TAREFAS ESPECÍFICAS**
1. **Criar workflow "Database_Schema_Generator" via n8n API**:
   - **PRIMEIRO**: Consultar Context 7 para tipos de nós Postgres atuais
   - **Manual Trigger**: Execução sob demanda
   - **Postgres Node**: Análise do schema atual
   - **Code Node**: Comparação com schema desejado
   - **Postgres Node**: Execução de migrations
   - **Code Node**: Validação de integridade
   - **Supabase Node**: Atualização de metadados

2. **Implementar migração do schema unificado**:
   ```sql
   -- Criar tabela users unificada
   CREATE TABLE IF NOT EXISTS users (
     cpf VARCHAR(11) PRIMARY KEY,
     phone VARCHAR NOT NULL,
     name VARCHAR NOT NULL,
     email VARCHAR,
     access_level INTEGER DEFAULT 1 CHECK (access_level BETWEEN 1 AND 7),
     geolocation JSONB,
     onboarding_progress JSONB DEFAULT '{"level": 1, "badges": [], "current_step": "welcome", "completed_steps": [], "gamification_score": 0}'::jsonb,
     active BOOLEAN DEFAULT true,
     last_access TIMESTAMP WITH TIME ZONE,
     metadata JSONB DEFAULT '{}'::jsonb,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

3. **Sistema de validação automática (PYTHON)**:
   ```python
   # Validação de integridade - USAR PYTHON
   def perform_validation_checks():
       validation_checks = {
           'foreign_keys': check_foreign_key_integrity(),
           'indexes': validate_indexes(),
           'constraints': check_constraints(),
           'data_types': validate_data_types(),
           'performance': analyze_query_performance()
       }
       return validation_checks

   def check_foreign_key_integrity():
       # Implementar verificação de FKs
       return {'status': 'ok', 'issues': []}

   def validate_indexes():
       # Implementar validação de índices
       return {'status': 'ok', 'count': 5}

   def check_constraints():
       # Implementar verificação de constraints
       return {'status': 'ok', 'violations': []}

   def validate_data_types():
       # Implementar validação de tipos
       return {'status': 'ok', 'mismatches': []}

   def analyze_query_performance():
       # Implementar análise de performance
       return {'status': 'ok', 'slow_queries': []}

   # Executar validação
   validation_results = perform_validation_checks()

   return validation_results
   ```

### **CRITÉRIOS DE SUCESSO**
- Schema unificado implementado
- Migração de dados sem perda
- Validação de integridade automática
- Performance otimizada
- Documentação automática gerada

### **ENTREGÁVEIS**
1. Workflow "Database_Schema_Generator" criado e ativo
2. Scripts de migração completos
3. Sistema de validação automática
4. Documentação do schema atualizada
5. Relatório de migração detalhado

Execute esta tarefa de forma independente e documente todos os passos realizados.
