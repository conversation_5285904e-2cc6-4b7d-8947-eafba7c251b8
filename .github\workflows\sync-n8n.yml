name: Sync n8n Workflows

on:
  # Execução automática a cada 6 horas
  schedule:
    - cron: '0 */6 * * *'
  
  # Execução manual via GitHub Actions UI
  workflow_dispatch:
    inputs:
      sync_type:
        description: 'Type of synchronization'
        required: true
        default: 'from-n8n'
        type: choice
        options:
          - from-n8n
          - to-n8n
          - bidirectional
      force_upload:
        description: 'Force upload to n8n (use with caution)'
        required: false
        default: false
        type: boolean

  # Execução em push para branch main (apenas validação)
  push:
    branches: [ main ]
    paths: 
      - 'workflows/active/**'
      - 'scripts/**'

  # Execução em pull request (apenas validação)
  pull_request:
    branches: [ main ]
    paths:
      - 'workflows/active/**'
      - 'scripts/**'

jobs:
  validate:
    name: Validate Workflows
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          npm install -g n8n
          
      - name: Validate workflow structure
        run: npm run workflows:validate
        
      - name: Check workflow syntax
        run: |
          for file in workflows/active/*.json; do
            if [ -f "$file" ]; then
              echo "Validating $file..."
              node -e "JSON.parse(require('fs').readFileSync('$file', 'utf8'))"
            fi
          done

  sync-from-n8n:
    name: Sync FROM n8n (Remote → Local)
    runs-on: ubuntu-latest
    needs: validate
    if: |
      (github.event_name == 'schedule') ||
      (github.event_name == 'workflow_dispatch' && 
       (github.event.inputs.sync_type == 'from-n8n' || github.event.inputs.sync_type == 'bidirectional'))
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          npm install -g n8n
          
      - name: Configure git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
      - name: Sync workflows from n8n
        env:
          N8N_API_URL: ${{ secrets.N8N_API_URL }}
          N8N_API_KEY: ${{ secrets.N8N_API_KEY }}
        run: npm run sync:from-n8n
        
      - name: Commit and push changes
        run: |
          if [ -n "$(git status --porcelain workflows/active/)" ]; then
            git add workflows/active/
            git commit -m "auto: sync workflows from n8n - $(date -u +%Y-%m-%dT%H:%M:%SZ)"
            git push
            echo "Changes committed and pushed"
          else
            echo "No changes to commit"
          fi

  sync-to-n8n:
    name: Sync TO n8n (Local → Remote)
    runs-on: ubuntu-latest
    needs: validate
    if: |
      (github.event_name == 'workflow_dispatch' && 
       (github.event.inputs.sync_type == 'to-n8n' || github.event.inputs.sync_type == 'bidirectional') &&
       github.event.inputs.force_upload == 'true')
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          npm install -g n8n
          
      - name: Sync workflows to n8n
        env:
          N8N_API_URL: ${{ secrets.N8N_API_URL }}
          N8N_API_KEY: ${{ secrets.N8N_API_KEY }}
          FORCE_UPLOAD: 'true'
        run: npm run sync:to-n8n

  sync-bidirectional:
    name: Bidirectional Sync (Local ↔ Remote)
    runs-on: ubuntu-latest
    needs: validate
    if: |
      (github.event_name == 'workflow_dispatch' && 
       github.event.inputs.sync_type == 'bidirectional')
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          npm install -g n8n
          
      - name: Configure git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
      - name: Run bidirectional sync
        env:
          N8N_API_URL: ${{ secrets.N8N_API_URL }}
          N8N_API_KEY: ${{ secrets.N8N_API_KEY }}
          SYNC_STRATEGY: 'remote_priority'
        run: npm run sync:full
        
      - name: Commit and push changes
        run: |
          if [ -n "$(git status --porcelain workflows/active/)" ]; then
            git add workflows/active/
            git commit -m "auto: bidirectional sync with n8n - $(date -u +%Y-%m-%dT%H:%M:%SZ)"
            git push
            echo "Changes committed and pushed"
          else
            echo "No changes to commit"
          fi

  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [validate, sync-from-n8n, sync-to-n8n, sync-bidirectional]
    if: always()
    
    steps:
      - name: Notify success
        if: |
          needs.validate.result == 'success' &&
          (needs.sync-from-n8n.result == 'success' || needs.sync-from-n8n.result == 'skipped') &&
          (needs.sync-to-n8n.result == 'success' || needs.sync-to-n8n.result == 'skipped') &&
          (needs.sync-bidirectional.result == 'success' || needs.sync-bidirectional.result == 'skipped')
        run: |
          echo "✅ Workflow synchronization completed successfully!"
          echo "All workflows are validated and synchronized."
          
      - name: Notify failure
        if: |
          needs.validate.result == 'failure' ||
          needs.sync-from-n8n.result == 'failure' ||
          needs.sync-to-n8n.result == 'failure' ||
          needs.sync-bidirectional.result == 'failure'
        run: |
          echo "❌ Workflow synchronization failed!"
          echo "Check the logs above for details."
          exit 1
