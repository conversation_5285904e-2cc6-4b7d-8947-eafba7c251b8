#!/usr/bin/env node

/**
 * WhatsApp AI Assistant Ecosystem Deployment Script
 * 
 * This script deploys the complete multi-hierarchical agent ecosystem
 * to an n8n instance using the n8n REST API.
 * 
 * Usage:
 *   node deploy_ecosystem.js [options]
 * 
 * Environment Variables:
 *   N8N_API_URL - n8n instance URL (e.g., https://your-n8n.com)
 *   N8N_API_KEY - n8n API key for authentication
 */

// Load environment variables first
require('dotenv').config();

const N8nApiClient = require('./n8n_api_client.js');
const path = require('path');
const fs = require('fs').promises;

// Configuration
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host/',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M',
  productFlowsPath: path.join(__dirname, '../product_flows'),
  infrastructureFlowsPath: path.join(__dirname, '../infrastructure_flows'),
  deploymentOptions: {
    activateAfterDeploy: true,
    continueOnError: true,
    delayBetweenDeployments: 2000 // 2 seconds between deployments
  }
};

// Workflow deployment order (dependencies first)
const DEPLOYMENT_ORDER = {
  infrastructure: [
    'Database_Schema_Generator_v1.json'
    // Optional files (will be skipped if not found):
    // 'Vector_Store_Setup_v1.json',
    // 'Data_Persistence_v1.json',
    // 'Monitoring_Logging_v1.json'
  ],
  product: [
    'SuperUser_Authentication_v1.json',
    'Session_Manager_v1.json',
    'Main_Orchestrator_Agent_v2.json', // Use enhanced orchestrator
    'Message_Processor_v1.json',
    'WhatsApp_Message_Router_v1.json' // Router last as it triggers the chain
  ]
};

class EcosystemDeployer {
  constructor() {
    this.client = new N8nApiClient(CONFIG.n8nApiUrl, CONFIG.n8nApiKey);
    this.deploymentLog = [];
  }

  // Validate environment and prerequisites
  async validateEnvironment() {
    console.log('🔍 Validating deployment environment...\n');
    
    const validations = [];
    
    // Check n8n API connection
    try {
      await this.client.getWorkflows();
      validations.push({ check: 'n8n API Connection', status: '✅ Connected' });
    } catch (error) {
      validations.push({ check: 'n8n API Connection', status: `❌ Failed: ${error.message}` });
      throw new Error('Cannot connect to n8n API. Please check N8N_API_URL and N8N_API_KEY.');
    }
    
    // Check workflow directories
    try {
      await fs.access(CONFIG.productFlowsPath);
      validations.push({ check: 'Product Flows Directory', status: '✅ Found' });
    } catch (error) {
      validations.push({ check: 'Product Flows Directory', status: '❌ Not Found' });
    }
    
    try {
      await fs.access(CONFIG.infrastructureFlowsPath);
      validations.push({ check: 'Infrastructure Flows Directory', status: '✅ Found' });
    } catch (error) {
      validations.push({ check: 'Infrastructure Flows Directory', status: '⚠️ Not Found (Optional)' });
    }
    
    // Display validation results
    console.log('📋 Environment Validation Results:');
    validations.forEach(v => console.log(`   ${v.check}: ${v.status}`));
    console.log('');
    
    return validations;
  }

  // Load workflows in specific order
  async loadWorkflowsInOrder(directoryPath, orderArray) {
    const workflows = [];
    
    for (const filename of orderArray) {
      try {
        const filePath = path.join(directoryPath, filename);
        const fileContent = await fs.readFile(filePath, 'utf8');
        const workflowData = JSON.parse(fileContent);
        
        workflows.push({
          filename,
          ...workflowData
        });
        
        console.log(`📄 Loaded: ${filename}`);
        
      } catch (error) {
        console.warn(`⚠️ Failed to load ${filename}: ${error.message}`);
      }
    }
    
    return workflows;
  }

  // Deploy with dependency management
  async deployWithDependencies() {
    console.log('🚀 Starting ecosystem deployment with dependency management...\n');
    
    const deploymentResults = {
      infrastructure: null,
      product: null,
      summary: null
    };
    
    try {
      // Phase 1: Infrastructure Flows
      console.log('📋 Phase 1: Deploying Infrastructure Flows...');
      const infrastructureFlows = await this.loadWorkflowsInOrder(
        CONFIG.infrastructureFlowsPath, 
        DEPLOYMENT_ORDER.infrastructure
      );
      
      if (infrastructureFlows.length > 0) {
        deploymentResults.infrastructure = await this.client.bulkDeployWorkflows(
          infrastructureFlows, 
          CONFIG.deploymentOptions
        );
      } else {
        console.log('⚠️ No infrastructure flows found, skipping...');
        deploymentResults.infrastructure = { summary: { total: 0, successful: 0, failed: 0 }, results: [] };
      }
      
      // Phase 2: Product Flows
      console.log('\n📋 Phase 2: Deploying Product Flows...');
      const productFlows = await this.loadWorkflowsInOrder(
        CONFIG.productFlowsPath, 
        DEPLOYMENT_ORDER.product
      );
      
      if (productFlows.length > 0) {
        deploymentResults.product = await this.client.bulkDeployWorkflows(
          productFlows, 
          CONFIG.deploymentOptions
        );
      } else {
        throw new Error('No product flows found! Cannot deploy ecosystem.');
      }
      
      // Generate summary
      deploymentResults.summary = this.generateDeploymentSummary(deploymentResults);
      
      return deploymentResults;
      
    } catch (error) {
      console.error('❌ Deployment failed:', error.message);
      throw error;
    }
  }

  // Generate deployment summary
  generateDeploymentSummary(results) {
    const infraSummary = results.infrastructure?.summary || { total: 0, successful: 0, failed: 0 };
    const productSummary = results.product?.summary || { total: 0, successful: 0, failed: 0 };
    
    const totalDeployed = infraSummary.total + productSummary.total;
    const totalSuccessful = infraSummary.successful + productSummary.successful;
    const totalFailed = infraSummary.failed + productSummary.failed;
    
    return {
      total_workflows: totalDeployed,
      successful_deployments: totalSuccessful,
      failed_deployments: totalFailed,
      success_rate: totalDeployed > 0 ? ((totalSuccessful / totalDeployed) * 100).toFixed(1) + '%' : '0%',
      infrastructure_flows: infraSummary,
      product_flows: productSummary,
      deployment_timestamp: new Date().toISOString()
    };
  }

  // Save deployment report
  async saveDeploymentReport(results) {
    const reportPath = path.join(__dirname, `deployment_report_${Date.now()}.json`);
    
    const report = {
      deployment_info: {
        timestamp: new Date().toISOString(),
        n8n_url: CONFIG.n8nApiUrl,
        deployer_version: '1.0.0'
      },
      environment_validation: this.validationResults,
      deployment_results: results,
      workflow_details: {
        infrastructure: results.infrastructure?.results || [],
        product: results.product?.results || []
      }
    };
    
    try {
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      console.log(`📄 Deployment report saved: ${reportPath}`);
    } catch (error) {
      console.warn('⚠️ Failed to save deployment report:', error.message);
    }
    
    return report;
  }

  // Main deployment method
  async deploy() {
    try {
      console.log('🤖 WhatsApp AI Assistant Ecosystem Deployer v1.0\n');
      console.log('🎯 Target n8n Instance:', CONFIG.n8nApiUrl);
      console.log('� API Key (first 20 chars):', CONFIG.n8nApiKey.substring(0, 20) + '...');
      console.log('�📁 Product Flows Path:', CONFIG.productFlowsPath);
      console.log('📁 Infrastructure Flows Path:', CONFIG.infrastructureFlowsPath);
      console.log('');
      
      // Validate environment
      this.validationResults = await this.validateEnvironment();
      
      // Deploy ecosystem
      const deploymentResults = await this.deployWithDependencies();
      
      // Save report
      const report = await this.saveDeploymentReport(deploymentResults);
      
      // Display final summary
      console.log('\n🎉 Ecosystem Deployment Completed!');
      console.log('=' .repeat(50));
      console.log(`📊 Total Workflows: ${deploymentResults.summary.total_workflows}`);
      console.log(`✅ Successful: ${deploymentResults.summary.successful_deployments}`);
      console.log(`❌ Failed: ${deploymentResults.summary.failed_deployments}`);
      console.log(`📈 Success Rate: ${deploymentResults.summary.success_rate}`);
      console.log('=' .repeat(50));
      
      if (deploymentResults.summary.failed_deployments > 0) {
        console.log('\n⚠️ Some workflows failed to deploy. Check the deployment report for details.');
        process.exit(1);
      } else {
        console.log('\n🚀 All workflows deployed successfully! Your WhatsApp AI Assistant is ready.');
        process.exit(0);
      }
      
    } catch (error) {
      console.error('\n💥 Deployment failed:', error.message);
      console.error('Please check your configuration and try again.');
      process.exit(1);
    }
  }
}

// CLI execution
if (require.main === module) {
  const deployer = new EcosystemDeployer();
  deployer.deploy();
}

module.exports = EcosystemDeployer;
