

# **Deploying a Persistent Google Gemini CLI Service with Docker and EasyPanel: A Comprehensive Technical Guide**

## **Part 1: Introduction \- The Paradigm Shift from Local CLI to Containerized Service**

### **The Modern CLI Dilemma**

The landscape of developer tooling is increasingly dominated by powerful, stateful command-line interfaces (CLIs) that serve as primary interaction points for complex services. The Google Gemini CLI is a prime example of this trend, offering developers direct, terminal-first access to the advanced capabilities of Gemini models, including the 1M token context window of Gemini 1.5 Pro.1 These tools are meticulously designed for interactive workflows, enabling tasks like code generation, debugging, and automation directly within a developer's local environment.1 However, this local-first design presents a significant challenge when attempting to integrate their functionality into automated, server-side applications. Their reliance on local configuration files and user-specific authentication states conflicts with the requirements of scalable, non-interactive backend systems.

### **The Containerization Imperative vs. Ephemerality**

Docker has emerged as the industry standard for creating reproducible, isolated, and portable application environments. The practice of containerizing applications solves dependencies and ensures consistency from development to production. Yet, a core principle of containerization is ephemerality: containers are designed to be stateless and disposable.4 Any data written to a container's ephemeral filesystem is permanently lost when the container is stopped, removed, or restarted. This fundamental characteristic is in direct opposition to the needs of a stateful tool like the Gemini CLI, which must persist its authentication tokens to function without repeated manual intervention. This report addresses and resolves this central conflict.

### **Architectural Vision: The Persistent Service Pattern**

The solution lies in a strategic architectural shift: transforming the Gemini CLI from a transient, interactive tool into a long-running, persistent microservice. This is achieved by hosting the CLI within a Docker container while externalizing its critical state—specifically, its authentication data—to a persistent Docker volume. The resulting container becomes a stable, daemonized service that can be accessed programmatically by other applications within the same environment. This effectively creates a private, authenticated Gemini API endpoint for your infrastructure, accessible via the Docker engine. This approach also introduces a valuable security boundary. The application that consumes the Gemini service, such as a Node.js web application, never needs direct access to the Google authentication credentials. Instead, it delegates requests to the secure, isolated Gemini container. This delegation model is a significant security enhancement over embedding sensitive tokens directly into a potentially web-facing application.

### **Target Audience and Prerequisites**

This technical guide is intended for Node.js developers who possess a working knowledge of Git, the command line, and fundamental Docker concepts. While the deployment will be demonstrated using the EasyPanel server control panel, the underlying principles are applicable to any container orchestration platform. Each step will be detailed to ensure clarity, regardless of prior experience with EasyPanel.5

## **Part 2: Filesystem Forensics \- Uncovering Gemini CLI's State**

### **Understanding Gemini CLI's Authentication Mechanisms**

The Gemini CLI offers several authentication methods, but two are primary for developers: OAuth 2.0 using a personal Google account and API keys generated from Google AI Studio or Google Cloud.1 The OAuth method is particularly advantageous for individual developers, as it provides a generous free tier of 60 requests per minute and access to powerful models like Gemini 1.5 Pro without the need to manage API keys.2 However, this method's primary design constraint for an automated environment is its reliance on an initial, interactive, browser-based login flow.8 A server-side container has no browser, which means the architecture must accommodate a one-time manual bootstrapping process. After this initial authentication, the CLI uses refresh tokens to maintain its session indefinitely in a headless state.

### **Locating the Configuration Directory**

The viability of creating a persistent service hinges entirely on identifying where the Gemini CLI stores its state after the OAuth flow is successfully completed. Official documentation and hands-on investigation reveal that the CLI creates a configuration directory in the user's home directory. On Linux-based systems, this path is \~/.gemini/.8 This directory is the "single point of state" for the application. It contains a

settings.json file, which stores user preferences like the selected theme, but more importantly, it is where the encrypted OAuth refresh tokens and other session data are stored. The \~ character resolves to the home directory of the user executing the process. This detail is of paramount importance for the subsequent Dockerfile construction, where a specific non-root user will be created.

### **The Persistence Target**

With the stateful component identified, the technical objective becomes clear: the \~/.gemini directory must be preserved across container lifecycles. This directory is the sole target for our persistence strategy. By mounting this specific path to an external, persistent Docker volume, the container can be stopped, restarted, or even completely replaced with a newer version, and it will immediately regain its authenticated state by reading the preserved token data from the volume. This technique of identifying a tool's configuration directory is a generalizable forensic pattern applicable to containerizing a wide array of CLI tools, such as the AWS CLI (\~/.aws), gcloud CLI (\~/.config/gcloud), and kubectl (\~/.kube).

## **Part 3: Architecting a Secure and Efficient Docker Image**

### **The Multi-Stage Build Strategy**

To create a production-ready container, it is essential to follow best practices that prioritize security and efficiency. The multi-stage build strategy is a cornerstone of this approach for Node.js applications.9 This technique involves using multiple

FROM instructions in a single Dockerfile. The initial stages are used for building and compiling, installing all necessary development dependencies. The final stage then copies only the essential, compiled artifacts into a clean base image, resulting in a significantly smaller and more secure production image by discarding the build-time environment.11

### **Dockerfile Construction \- Line by Line**

The following Dockerfile implements a multi-stage build to create a lean, secure, and persistent-ready image for the Gemini CLI.

Dockerfile

\# Stage 1: The Builder  
\# Use a specific, slim, Debian-based Node.js version for compatibility and size.  
FROM node:20\-slim AS builder

\# Set a working directory for the build stage.  
WORKDIR /app

\# Install the Gemini CLI globally within this temporary build environment.  
RUN npm install \-g @google/gemini-cli@latest

\# Stage 2: The Production Image  
\# Start fresh from the same lean base image to ensure a clean runtime.  
FROM node:20\-slim

\# Create a non-root user 'node' with a home directory at /home/<USER>
\# This is a critical security best practice.  
RUN useradd \--create-home \--shell /bin/bash node

\# Switch to the non-root user for all subsequent operations.  
USER node

\# Copy the compiled 'gemini' binary from the builder stage.  
COPY \--from=builder /usr/local/bin/gemini /usr/local/bin/

\# Copy the globally installed node\_modules required by the binary.  
COPY \--from=builder /usr/local/lib/node\_modules /usr/local/lib/node\_modules

\# Set the working directory to the user's home directory.  
\# This is where the CLI will expect to find the \~/.gemini directory.  
WORKDIR /home/<USER>

\# Use a command that runs indefinitely to keep the container alive.  
\# This transforms the container into a long-running service.  
CMD \["tail", "-f", "/dev/null"\]

### **The.dockerignore File**

To optimize the build process and prevent sensitive information from being copied into the build context, a .dockerignore file should be created in the same directory as the Dockerfile. This file functions similarly to .gitignore.

\#.dockerignore  
node\_modules  
.git  
.env  
\*.log

This prevents local development dependencies and Git history from being sent to the Docker daemon, speeding up the build and reducing the risk of credential leakage.11

### **Table: Dockerfile Best Practices Implementation**

The constructed Dockerfile deliberately incorporates several industry-standard best practices. The following table summarizes these decisions and their rationale.

| Best Practice | Dockerfile Implementation | Rationale & Source Reference |
| :---- | :---- | :---- |
| **Use Multi-Stage Builds** | FROM... AS builder, COPY \--from=builder... | Reduces final image size and attack surface by discarding build-time dependencies. 9 |
| **Use Minimal Base Images** | FROM node:20-slim | The slim variant (Debian-based) offers a superior balance of small size and high compatibility over alpine (musl-based), avoiding potential glibc-related issues with complex native modules. 13 |
| **Use Specific Image Tags** | node:20-slim (not :latest) | Ensures deterministic and reproducible builds, preventing unexpected upstream changes from breaking the container. 10 |
| **Run as Non-Root User** | RUN useradd..., USER node | Adheres to the principle of least privilege, significantly improving the container's security posture by limiting the potential impact of a compromise. 12 |
| **Keep Container Running** | CMD \["tail", "-f", "/dev/null"\] | A container exits when its main process (PID 1\) terminates. This command provides a lightweight, non-terminating process, transforming the container into a persistent daemon against which docker exec commands can be run. 15 |

## **Part 4: Deployment and Persistence on EasyPanel**

### **Setting up the Project**

Deployment begins by preparing a Git repository (e.g., on GitHub) containing the Dockerfile and .dockerignore file created in the previous section. Within the EasyPanel dashboard, the first step is to create a new project, which serves as a logical grouping for related services.17

### **Creating the App Service**

With the project established, a new service can be added to host the Gemini CLI container.

1. From the project dashboard, select **\+ Service**.  
2. Choose **App** as the service type.4  
3. In the **Source** tab, configure the service to use your Git repository. EasyPanel will automatically detect the presence of a Dockerfile and use it as the build method, overriding other buildpack options.5

### **Configuring the Persistent Volume Mount (The Critical Step)**

This is the most crucial configuration step, where the container's ephemeral storage is linked to a persistent volume managed by the host. EasyPanel simplifies this process through its UI, abstracting the underlying Docker commands.5

1. Navigate to the **Mounts** tab for the newly created app service.4  
2. Click **Add Mount** and select the **Volume** type. A named volume is the appropriate choice for application data that should be managed by Docker and persist independently of the container.19  
3. In the **name** field, provide a descriptive identifier, such as gemini-auth-data. EasyPanel will manage the physical location of this volume on the host server, typically within /etc/easypanel/projects/....4  
4. In the **mountPath** field, enter the absolute path inside the container: /home/<USER>/.gemini.

This configuration directly connects the forensic work from Part 2 with the Docker image architecture from Part 3\. The mountPath of /home/<USER>/.gemini corresponds precisely to the home directory of the node user created in the Dockerfile. This instruction tells the Docker engine to intercept any file system operations (reads or writes) directed at that internal path and redirect them to the persistent gemini-auth-data volume on the host.

### **Deploying the Service**

After configuring the mount, click **Deploy**. EasyPanel will execute the following sequence:

1. Clone the specified Git repository.  
2. Build a Docker image using the provided Dockerfile.  
3. Create a persistent Docker Swarm volume named gemini-auth-data.  
4. Start the container as a Docker Swarm service, attaching the persistent volume to the specified mount path.

EasyPanel's use of Docker Swarm also provides an important networking benefit: each service is assigned a stable, DNS-resolvable name within a private project network.20 This allows other services within the same project to communicate with the Gemini CLI service by its name (e.g.,

gemini-cli-service), eliminating the need for hard-coded IP addresses.

## **Part 5: One-Time Authentication and Persistence Verification**

### **Accessing the Running Container**

With the service deployed and running, the one-time authentication must be performed. EasyPanel provides an in-browser terminal for this purpose.

1. Navigate to the Gemini CLI service in the EasyPanel dashboard.  
2. Select the **Console** tab.  
3. This opens a shell session directly inside the running container, which is equivalent to executing docker exec \-it \<container\_id\> /bin/bash from the host's command line.4

### **Initiating the OAuth Flow**

Inside the container's console, the initial authentication can now be triggered.

1. Execute the gemini command.  
2. The CLI will detect that it is not authenticated and will prompt for the desired method. Select the OAuth option.  
3. It will then display a URL and a user code in the terminal.8  
4. Copy the full URL and paste it into the browser on your local machine.  
5. Complete the Google sign-in process and grant the requested permissions.  
6. Upon success, the CLI running inside the container's console will automatically detect the completed authentication and present its interactive prompt.

### **Verifying State Creation**

To confirm that the authentication state was written to the correct location, run ls \-la \~/.gemini inside the container's console. The output should show a settings.json file and potentially other token-related files. This confirms that the CLI has successfully written its state to the path that is being redirected to the persistent volume. The interactive session can now be exited.

### **The Litmus Test: Verifying Persistence**

The final and most important test is to verify that this state survives a complete container replacement.

1. In the EasyPanel UI for the service, navigate to the **General** tab.  
2. Click the **Restart** button. This action instructs Docker Swarm to terminate the existing container and schedule a new one based on the same image and configuration.  
3. Once the service status shows as running, open the **Console** again.  
4. Execute the gemini command.

If the configuration is correct, the CLI will start immediately and display its interactive prompt without asking for authentication. This is the definitive proof that the new, clean container successfully mounted the existing volume, read the persisted OAuth tokens from \~/.gemini/settings.json, and established an authenticated session. The service is now robust and persistent.

## **Part 6: Programmatic Integration with a Node.js Application**

### **The Use Case**

Consider a common scenario: a separate Node.js application, such as an Express API, is running as another service within the same EasyPanel project. This application needs to leverage the Gemini CLI to perform a task, like summarizing user-provided text, without containing any Gemini-related dependencies or credentials itself.

### **Using child\_process**

The Node.js child\_process module provides the necessary functionality to execute external shell commands.21 The

exec function is well-suited for this task, as it can run a command and buffer its output, returning it via a callback.22

The command to be executed will be docker exec \<service\_name\> gemini \-p "Your prompt here". The \<service\_name\> is not a transient container ID but the stable, DNS-resolvable service name assigned by EasyPanel's internal Docker Swarm network (e.g., gemini-cli). This makes the integration highly robust against container restarts or IP address changes. For use with modern async/await syntax, the exec function can be promisified using Node's built-in util module.23

### **Example Express Route**

The following code demonstrates an Express.js route that accepts text in a POST request, securely constructs a command, executes it against the persistent Gemini CLI service, and returns the result.

JavaScript

const express \= require('express');  
const util \= require('util');  
const { exec } \= require('child\_process');

const app \= express();  
app.use(express.json());

// Promisify the exec function for async/await syntax  
const execPromise \= util.promisify(exec);

app.post('/summarize', async (req, res) \=\> {  
  const { text } \= req.body;

  if (\!text) {  
    return res.status(400).json({ error: 'Text is required' });  
  }

  // IMPORTANT: Sanitize input to prevent command injection vulnerabilities.  
  // This is a basic example; a robust library should be used in production.  
  const sanitizedPrompt \= text.replace(/"/g, '\\\\"');  
    
  // The DNS-resolvable name of the Gemini CLI service in EasyPanel.  
  const serviceName \= 'gemini-cli'; 

  const command \= \`docker exec ${serviceName} gemini \-p "Summarize this text: ${sanitizedPrompt}"\`;

  try {  
    const { stdout, stderr } \= await execPromise(command);

    if (stderr) {  
      console.error('Gemini CLI stderr:', stderr);  
      return res.status(500).json({ error: 'An error occurred with the Gemini CLI', details: stderr });  
    }

    res.json({ summary: stdout.trim() });  
  } catch (error) {  
    console.error('Execution error:', error);  
    res.status(500).json({ error: 'Failed to execute command', details: error.message });  
  }  
});

const PORT \= process.env.PORT |

| 3000;  
app.listen(PORT, () \=\> {  
  console.log(\`Server is running on port ${PORT}\`);  
});

### **Security and Architectural Considerations**

A critical security note is the risk of **command injection**. When constructing shell commands from user-provided input, that input must be rigorously sanitized to prevent malicious commands from being executed. The example shows a basic sanitation step, but a production application should employ a dedicated argument-sanitizing library.

Architecturally, this pattern establishes the gemini-cli container as a secure **API Façade**. The Node.js application is completely decoupled from the Gemini tooling; it has no dependency on the @google/gemini-cli npm package or any Google SDKs. Its only dependency is the ability to execute a Docker command. This allows the Gemini CLI service to be updated, reconfigured, or even replaced with an entirely different tool, and as long as the command-line interface remains compatible, the calling application requires no changes.

While EasyPanel allows for scaling services to multiple replicas 4, this specific stateful architecture is best suited for a single-replica deployment. Scaling to multiple replicas would require all containers to share the same volume, which could lead to file-locking issues or race conditions when writing to the

settings.json file. For this use case, a single, highly available, persistent replica provides the most stable and effective architecture.

## **Part 7: Conclusion \- A Reusable Pattern for Stateful Tooling**

This guide has detailed the complete process of transforming the Google Gemini CLI from an interactive, local-first tool into a persistent, containerized microservice. By forensically identifying the CLI's state directory, architecting a secure and lean multi-stage Docker image, and leveraging the persistence and networking features of EasyPanel, a robust and programmatically accessible service was created. The critical challenge of persisting OAuth authentication across container restarts was solved using a named Docker volume, verified through a systematic restart and test procedure.

The architectural pattern demonstrated here—**Identify State, Containerize with Keep-Alive, Persist with Volumes, and Access with exec**—is a powerful and reusable blueprint that extends far beyond the Gemini CLI. It provides a reliable method for integrating a vast ecosystem of stateful command-line tools into modern, container-based, server-side applications.

For a production environment, further enhancements could include configuring EasyPanel's notification system to monitor deployment status 24, implementing more sophisticated logging and error handling within the calling Node.js application, and hardening security by running the Docker daemon with a dedicated, least-privilege service account. By adopting this pattern, developers can effectively bridge the gap between interactive CLIs and the demands of automated, service-oriented architectures.

#### **Referências citadas**

1. google/gemini-cli \- NPM, acessado em setembro 19, 2025, [https://www.npmjs.com/package/@google/gemini-cli](https://www.npmjs.com/package/@google/gemini-cli)  
2. google-gemini/gemini-cli: An open-source AI agent that brings the power of Gemini directly into your terminal. \- GitHub, acessado em setembro 19, 2025, [https://github.com/google-gemini/gemini-cli](https://github.com/google-gemini/gemini-cli)  
3. Gemini CLI | Gemini Code Assist \- Google for Developers, acessado em setembro 19, 2025, [https://developers.google.com/gemini-code-assist/docs/gemini-cli](https://developers.google.com/gemini-code-assist/docs/gemini-cli)  
4. App Service \- Easypanel, acessado em setembro 19, 2025, [https://easypanel.io/docs/services/app](https://easypanel.io/docs/services/app)  
5. Easypanel \- Modern Server Control Panel, acessado em setembro 19, 2025, [https://easypanel.io/](https://easypanel.io/)  
6. Easypanel \- A Modern Server Control Panel which uses Docker \- YouTube, acessado em setembro 19, 2025, [https://www.youtube.com/watch?v=8D8E5k\_aG\_o](https://www.youtube.com/watch?v=8D8E5k_aG_o)  
7. google/gemini-cli-core \- NPM, acessado em setembro 19, 2025, [https://www.npmjs.com/package/@google/gemini-cli-core?activeTab=readme](https://www.npmjs.com/package/@google/gemini-cli-core?activeTab=readme)  
8. Hands-on with Gemini CLI \- Codelabs, acessado em setembro 19, 2025, [https://codelabs.developers.google.com/gemini-cli-hands-on](https://codelabs.developers.google.com/gemini-cli-hands-on)  
9. 10 best practices to containerize Node.js web applications with Docker | Snyk Blog, acessado em setembro 19, 2025, [https://snyk.io/blog/10-best-practices-to-containerize-nodejs-web-applications-with-docker/](https://snyk.io/blog/10-best-practices-to-containerize-nodejs-web-applications-with-docker/)  
10. Docker best practices with Node.js \- Medium, acessado em setembro 19, 2025, [https://medium.com/@nodepractices/docker-best-practices-with-node-js-e044b78d8f67](https://medium.com/@nodepractices/docker-best-practices-with-node-js-e044b78d8f67)  
11. Best practices for reducing Docker image size for Node.js apps? \- DigitalOcean, acessado em setembro 19, 2025, [https://www.digitalocean.com/community/questions/best-practices-for-reducing-docker-image-size-for-node-js-apps](https://www.digitalocean.com/community/questions/best-practices-for-reducing-docker-image-size-for-node-js-apps)  
12. NodeJS Docker \- OWASP Cheat Sheet Series, acessado em setembro 19, 2025, [https://cheatsheetseries.owasp.org/cheatsheets/NodeJS\_Docker\_Cheat\_Sheet.html](https://cheatsheetseries.owasp.org/cheatsheets/NodeJS_Docker_Cheat_Sheet.html)  
13. Avoid Using “bloated” Node.js Docker Image in Production\! \- DEV Community, acessado em setembro 19, 2025, [https://dev.to/ptuladhar3/avoid-using-bloated-nodejs-docker-image-in-production-3doc](https://dev.to/ptuladhar3/avoid-using-bloated-nodejs-docker-image-in-production-3doc)  
14. A Deeper Look into Node.js Docker Images: Help, My Node Image Has Python\!, acessado em setembro 19, 2025, [https://labs.iximiuz.com/tutorials/how-to-choose-nodejs-container-image](https://labs.iximiuz.com/tutorials/how-to-choose-nodejs-container-image)  
15. 5 Methods to Keep Docker Container Running for Debugging \- Spacelift, acessado em setembro 19, 2025, [https://spacelift.io/blog/docker-keep-container-running](https://spacelift.io/blog/docker-keep-container-running)  
16. How to Keep Docker Container Running for Debugging \- DevOpsCube, acessado em setembro 19, 2025, [https://devopscube.com/keep-docker-container-running/](https://devopscube.com/keep-docker-container-running/)  
17. Deploying an Express.js Application with Easypanel, acessado em setembro 19, 2025, [https://easypanel.io/docs/quickstarts/express](https://easypanel.io/docs/quickstarts/express)  
18. Builders | Easypanel, acessado em setembro 19, 2025, [https://easypanel.io/docs/builders](https://easypanel.io/docs/builders)  
19. Volumes \- Docker Docs, acessado em setembro 19, 2025, [https://docs.docker.com/engine/storage/volumes/](https://docs.docker.com/engine/storage/volumes/)  
20. Getting Started \- Easypanel, acessado em setembro 19, 2025, [https://easypanel.io/docs](https://easypanel.io/docs)  
21. Child process | Node.js v24.8.0 Documentation, acessado em setembro 19, 2025, [https://nodejs.org/api/child\_process.html](https://nodejs.org/api/child_process.html)  
22. Child Processes in Node.js: spawn, exec, fork and Use Cases | by Aditya Yadav | Medium, acessado em setembro 19, 2025, [https://dev-aditya.medium.com/child-processes-in-node-js-spawn-exec-fork-and-use-cases-6eab4ddb9dcf](https://dev-aditya.medium.com/child-processes-in-node-js-spawn-exec-fork-and-use-cases-6eab4ddb9dcf)  
23. Issue Running docker command inside a node.js container \- Reddit, acessado em setembro 19, 2025, [https://www.reddit.com/r/docker/comments/yws3rk/issue\_running\_docker\_command\_inside\_a\_nodejs/](https://www.reddit.com/r/docker/comments/yws3rk/issue_running_docker_command_inside_a_nodejs/)  
24. Notifications \- Easypanel, acessado em setembro 19, 2025, [https://easypanel.io/docs/guides/notifications](https://easypanel.io/docs/guides/notifications)