#!/usr/bin/env python3
"""
N8N Workflows Validation Script
Valida estado atual dos workflows n8n via API
"""

import requests
import json
from datetime import datetime

class N8nWorkflowValidator:
    def __init__(self):
        self.api_url = "https://n8n-n8n.w9jo16.easypanel.host/api/v1"
        self.api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M"
        self.headers = {
            "X-N8N-API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
        
        # Workflows esperados conforme arquitetura Unified_User_Pipeline
        self.expected_workflows = [
            "Unified_User_Pipeline",
            "Normalize_Extract_Subflow", 
            "User_Registry_Access_Subflow",
            "Session_Manager_Subflow",
            "Onboarding_Orchestrator_Subflow",
            "Message_Processor_Subflow",
            "Persistence_Memory_Subflow"
        ]

    def get_workflows(self):
        """Obtém lista de workflows do n8n"""
        try:
            response = requests.get(
                f"{self.api_url}/workflows",
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ Erro ao obter workflows: {e}")
            return None

    def validate_workflow_architecture(self):
        """Valida se a arquitetura atual está alinhada com a documentação"""
        print("🔍 Validando arquitetura de workflows n8n...")
        
        workflows_data = self.get_workflows()
        if not workflows_data:
            return None
        
        workflows = workflows_data.get('data', [])
        print(f"📊 Total de workflows encontrados: {len(workflows)}")
        
        # Análise dos workflows
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'total_workflows': len(workflows),
            'active_workflows': 0,
            'found_expected': [],
            'missing_expected': [],
            'unexpected_workflows': [],
            'webhook_workflows': [],
            'architecture_status': 'unknown'
        }
        
        workflow_names = []
        for workflow in workflows:
            name = workflow.get('name', '')
            workflow_names.append(name)
            
            if workflow.get('active', False):
                analysis['active_workflows'] += 1
            
            # Verificar se é um dos workflows esperados
            if any(expected in name for expected in self.expected_workflows):
                analysis['found_expected'].append({
                    'name': name,
                    'id': workflow.get('id'),
                    'active': workflow.get('active', False)
                })
            else:
                analysis['unexpected_workflows'].append({
                    'name': name,
                    'id': workflow.get('id'),
                    'active': workflow.get('active', False)
                })
        
        # Verificar workflows esperados que não foram encontrados
        for expected in self.expected_workflows:
            if not any(expected in name for name in workflow_names):
                analysis['missing_expected'].append(expected)
        
        # Determinar status da arquitetura
        if len(analysis['found_expected']) >= 5:  # Pelo menos 5 dos 7 esperados
            analysis['architecture_status'] = 'unified_pipeline_implemented'
        elif any('Router' in name or 'Orchestrator' in name for name in workflow_names):
            analysis['architecture_status'] = 'legacy_architecture'
        else:
            analysis['architecture_status'] = 'unknown_architecture'
        
        return analysis

    def generate_validation_report(self):
        """Gera relatório de validação dos workflows"""
        analysis = self.validate_workflow_architecture()
        if not analysis:
            return None
        
        print(f"\n📋 RELATÓRIO DE VALIDAÇÃO:")
        print(f"Total de workflows: {analysis['total_workflows']}")
        print(f"Workflows ativos: {analysis['active_workflows']}")
        print(f"Status da arquitetura: {analysis['architecture_status']}")
        
        print(f"\n✅ Workflows esperados encontrados ({len(analysis['found_expected'])}):")
        for workflow in analysis['found_expected']:
            status = "🟢 ATIVO" if workflow['active'] else "🔴 INATIVO"
            print(f"  • {workflow['name']} - {status}")
        
        if analysis['missing_expected']:
            print(f"\n❌ Workflows esperados não encontrados ({len(analysis['missing_expected'])}):")
            for missing in analysis['missing_expected']:
                print(f"  • {missing}")
        
        if analysis['unexpected_workflows']:
            print(f"\n⚠️ Workflows não esperados ({len(analysis['unexpected_workflows'])}):")
            for workflow in analysis['unexpected_workflows'][:5]:  # Mostrar apenas os primeiros 5
                status = "🟢 ATIVO" if workflow['active'] else "🔴 INATIVO"
                print(f"  • {workflow['name']} - {status}")
        
        # Salvar relatório
        report_path = "/mnt/persist/workspace/docs/architecture/N8N_WORKFLOWS_VALIDATION_REPORT.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 Relatório salvo em: {report_path}")
        return analysis

    def check_webhook_status(self):
        """Verifica status específico de webhooks"""
        workflows_data = self.get_workflows()
        if not workflows_data:
            return None
        
        webhook_info = []
        for workflow in workflows_data.get('data', []):
            if 'webhook' in workflow.get('name', '').lower() or 'router' in workflow.get('name', '').lower():
                webhook_info.append({
                    'name': workflow.get('name'),
                    'id': workflow.get('id'),
                    'active': workflow.get('active', False),
                    'updated_at': workflow.get('updatedAt')
                })
        
        return webhook_info

def main():
    """Função principal"""
    print("🤖 N8N Workflows Validator - AI Comtxae")
    print("=" * 50)
    
    validator = N8nWorkflowValidator()
    
    # Executar validação
    report = validator.generate_validation_report()
    
    if report:
        # Verificar webhooks específicos
        webhook_info = validator.check_webhook_status()
        if webhook_info:
            print(f"\n🔗 Webhooks encontrados ({len(webhook_info)}):")
            for webhook in webhook_info:
                status = "🟢 ATIVO" if webhook['active'] else "🔴 INATIVO"
                print(f"  • {webhook['name']} - {status}")
        
        # Recomendações baseadas na análise
        if report['architecture_status'] == 'unified_pipeline_implemented':
            print(f"\n✅ ARQUITETURA ALINHADA: Unified_User_Pipeline implementada")
        elif report['architecture_status'] == 'legacy_architecture':
            print(f"\n⚠️ ARQUITETURA LEGADO: Migração para Unified_User_Pipeline necessária")
        else:
            print(f"\n❓ ARQUITETURA DESCONHECIDA: Análise manual necessária")
    
    return report

if __name__ == "__main__":
    main()
