# 🚀 PLANO DE IMPLEMENTAÇÃO - AGENTES REMOTOS

**Data**: 18/09/2025  
**Objetivo**: Implementação dos 8 workflows funcionais do AI Comtxae  
**Estratégia**: Execução sequencial com validação por etapa  

---

## 📋 DOCUMENTAÇÃO ESSENCIAL

### **ARQUIVOS PRINCIPAIS**
1. **`remote_agents_unificados.md`** - Prompts dos 8 agentes especializados
2. **`docs/architecture/NUMERACAO_SUBFLUXOS_ETAPAS.md`** - Fluxo sequencial (Etapas 0-6)
3. **`docs/architecture/REORGANIZACAO_SCHEMA_SUPABASE.md`** - Schema unificado (16 tabelas)
4. **`docs/planning/QUESTIONARIO_LACUNAS_INFORMACAO.md`** - 95 respostas técnicas

### **ARQUIVOS DE APOIO**
- **`docs/analysis/VERIFICACAO_REMOTE_AGENTS_5_6_7.md`** - <PERSON><PERSON><PERSON><PERSON> a<PERSON>di<PERSON>
- **`docs/summary/VERIFICACAO_CORRIGIDA_REMOTE_AGENTS.md`** - Status atual
- **`docs/architecture/ARCHITECTURE_VALIDATION.md`** - Validação arquitetural

---

## 🎯 ORDEM DE EXECUÇÃO DOS AGENTES

### **FASE 1: INFRAESTRUTURA (Agente 8)**
**`Agent 8 - Database_Schema_Generator`**
- **Prioridade**: CRÍTICA
- **Objetivo**: Unificar schema das 16 tabelas
- **Entrega**: Schema funcional com CPF como PK

### **FASE 2: WORKFLOW PRINCIPAL (Agente 1)**
**`Agent 1 - Unified_User_Pipeline`**
- **Prioridade**: CRÍTICA
- **Objetivo**: Webhook único + roteamento por access_level
- **Entrega**: Entrada única funcional

### **FASE 3: SUBFLUXOS SEQUENCIAIS (Agentes 2-7)**
**Ordem obrigatória:**
1. **Agent 2** - Normalize_Extract_Subflow (Etapa 1)
2. **Agent 3** - User_Registry_Access_Subflow (Etapa 2)
3. **Agent 4** - Session_Manager_Subflow (Etapa 3)
4. **Agent 5** - Onboarding_Orchestrator_Subflow (Etapa 4)
5. **Agent 6** - Message_Processor_Subflow (Etapa 5)
6. **Agent 7** - Persistence_Memory_Subflow (Etapa 6)

---

## ✅ CRITÉRIOS DE VALIDAÇÃO POR FASE

### **FASE 1 - Schema Database**
- [ ] 16 tabelas unificadas com CPF como PK
- [ ] Relacionamentos funcionais (FKs corretas)
- [ ] Migração de dados legados concluída
- [ ] Índices de performance otimizados

### **FASE 2 - Workflow Principal**
- [ ] Único webhook ativo no sistema
- [ ] Roteamento por access_level (1-7) funcional
- [ ] Execução dos 6 subfluxos configurada
- [ ] Tempo de resposta < 3 segundos

### **FASE 3 - Subfluxos**
**Para cada subfluxo:**
- [ ] Workflow criado e ativo no n8n
- [ ] Nós conectados funcionalmente
- [ ] Código Python nos nós Code
- [ ] Teste individual aprovado
- [ ] Integração com subfluxo anterior

---

## 🔧 ESPECIFICAÇÕES TÉCNICAS

### **CONFIGURAÇÕES OBRIGATÓRIAS**
- **N8N API**: https://n8n-n8n.w9jo16.easypanel.host
- **Supabase**: https://qvkstxeayyzrntywifdi.supabase.co
- **Context 7 MCP**: `/n8n-io/n8n-docs` (obrigatório antes de criar nós)
- **Linguagem**: Python para todos os nós Code

### **REGRAS DE IMPLEMENTAÇÃO**
1. **Execução recorrente**: Agentes executam até workflow 100% funcional
2. **Nomenclatura**: Renomear fluxos existentes ou criar novos com nomes corretos
3. **Nós corretos**: Supabase (CRUD), Postgres (queries), HTTP (APIs)
4. **Validação**: Teste individual + integração antes de próximo agente

---

## 📊 MÉTRICAS DE SUCESSO

### **SISTEMA COMPLETO**
- ✅ **8 workflows funcionais** (1 principal + 6 subfluxos + 1 utilitário)
- ✅ **Webhook único** (apenas Unified_User_Pipeline)
- ✅ **Schema unificado** (16 tabelas conectadas)
- ✅ **Tempo de resposta** < 3 segundos
- ✅ **Taxa de sucesso** > 95%

### **POR WORKFLOW**
- ✅ **Nós conectados** funcionalmente
- ✅ **Código Python** em todos os nós Code
- ✅ **Teste individual** aprovado
- ✅ **Integração** com outros workflows
- ✅ **Performance** dentro do esperado

---

## 🚨 PONTOS DE ATENÇÃO

### **LIÇÕES APRENDIDAS**
- **Agents 5, 6, 7** executaram com sucesso em branches separadas
- **Verificação inicial** falhou por não sincronizar branches remotas
- **Execução recorrente** necessária até workflows 100% funcionais

### **RISCOS IDENTIFICADOS**
- **Workflows existentes** podem ter nomenclatura incorreta
- **Nós deprecated** podem estar em uso
- **Conexões quebradas** entre nós
- **Código JavaScript** precisa ser convertido para Python

### **MITIGAÇÕES**
- **Execução recorrente** até funcionalidade completa
- **Validação obrigatória** via Context 7 MCP
- **Teste individual** de cada workflow
- **Nomenclatura padronizada** obrigatória

---

## 📅 CRONOGRAMA ESTIMADO

### **FASE 1 - Schema (1 dia)**
- Agent 8: Database_Schema_Generator

### **FASE 2 - Principal (1 dia)**
- Agent 1: Unified_User_Pipeline

### **FASE 3 - Subfluxos (6 dias)**
- Agent 2: Normalize_Extract_Subflow
- Agent 3: User_Registry_Access_Subflow
- Agent 4: Session_Manager_Subflow
- Agent 5: Onboarding_Orchestrator_Subflow
- Agent 6: Message_Processor_Subflow
- Agent 7: Persistence_Memory_Subflow

### **TOTAL: 8 dias úteis**

---

## 🎯 PRÓXIMOS PASSOS

1. **Executar Agent 8** (Database_Schema_Generator)
2. **Validar schema** unificado
3. **Executar Agent 1** (Unified_User_Pipeline)
4. **Testar webhook** único
5. **Executar Agents 2-7** sequencialmente
6. **Validar sistema** completo
7. **Deploy** em produção

---

**Status**: Plano de implementação criado  
**Documentação**: Enxuta e focada na execução  
**Próximo passo**: Executar Agent 8 para unificar schema
